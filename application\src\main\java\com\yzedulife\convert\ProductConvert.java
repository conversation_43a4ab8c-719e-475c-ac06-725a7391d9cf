package com.yzedulife.convert;

import com.yzedulife.service.dto.ProductDTO;
import com.yzedulife.response.ProductResponse;
import com.yzedulife.vo.MPProductVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
@Mapper
public interface ProductConvert {
    ProductConvert INSTANCE = Mappers.getMapper(ProductConvert.class);

    ProductDTO vo2dto(MPProductVO productVO);

    ProductResponse dto2res(ProductDTO productDTO);
    List<ProductResponse> dto2resBatch(List<ProductDTO> productDTOs);
}
