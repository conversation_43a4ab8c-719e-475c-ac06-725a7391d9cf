package com.yzedulife.convert;

import com.yzedulife.service.dto.MPProductDTO;
import com.yzedulife.response.MPProductResponse;
import com.yzedulife.vo.MPProductVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
@Mapper
public interface MPProductConvert {
    MPProductConvert INSTANCE = Mappers.getMapper(MPProductConvert.class);

    MPProductResponse dto2res(MPProductDTO mpProductDTO);
    MPProductDTO vo2dto(MPProductVO mpProductVO);
    List<MPProductResponse> dto2resBatch(List<MPProductDTO> mpProductDTOs);
}
