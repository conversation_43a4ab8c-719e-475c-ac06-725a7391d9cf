package com.yzedulife.convert;

import com.yzedulife.service.dto.MPOrderDTO;
import com.yzedulife.response.MPOrderResponse;
import com.yzedulife.vo.MPOrderVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MPOrderConvert {
    MPOrderConvert INSTANCE = Mappers.getMapper(MPOrderConvert.class);

    MPOrderDTO vo2dto(MPOrderVO mpOrderVO);

    MPOrderResponse dto2res(MPOrderDTO mpOrderDTO);
    List<MPOrderResponse> dto2resBatch(List<MPOrderDTO> mpOrderDTOs);
}