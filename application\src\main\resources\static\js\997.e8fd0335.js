"use strict";(self["webpackChunkadmin_web"]=self["webpackChunkadmin_web"]||[]).push([[997],{997:function(e,l,a){a.r(l),a.d(l,{default:function(){return C}});a(5226),a(7335),a(499);var t=a(4922),o=a(1417),u=a(2959),n=a(1402),r=a(7415),s=a(9077);const i=()=>(0,s.A)({url:"/queryConfigs",method:"get"}),c=e=>(0,s.A)({url:"/modifyConfig",method:"post",data:e}),k=e=>{const l=new FormData;return l.append("photo",e),(0,s.A)({url:"/uploadStorePhoto",method:"post",data:l,headers:{"Content-Type":"multipart/form-data"}})},d={class:"settings-view"},p={key:0,class:"store-photo-container"},v=["onClick"],y={key:0,class:"multiline-text"},f={key:1};var h=(0,t.pM)({__name:"SettingsView",setup(e){const l=(0,n.KR)([]),a=(0,n.KR)(!1),s=(0,n.KR)(""),h=(0,n.KR)(""),m=(0,n.KR)(null),w="https://api.yzedulife.com/bohua/storePhoto",C=()=>{m.value?.click()},b=async e=>{const l=e.target;if(!l.files?.length)return;const t=l.files[0],o=t.type.startsWith("image/"),u=t.size/1024/1024<2;if(o)if(u)try{a.value=!0,await k(t),r.nk.success("上传成功"),window.location.reload()}catch(n){r.nk.error("上传失败"),console.error("上传失败:",n)}finally{a.value=!1,l.value=""}else r.nk.error("图片大小不能超过 2MB!");else r.nk.error("只能上传图片文件!")},g=async()=>{a.value=!0;try{l.value=(await i()).data,l.value.some((e=>"store_photo"===e.key))||l.value.push({key:"store_photo",name:"门店照片",value:""})}catch(e){r.nk.error("获取配置失败"),console.error("获取配置失败:",e)}finally{a.value=!1}};(0,t.sV)(g);const _=e=>["announce"].some((l=>e.toLowerCase().includes(l))),X=e=>{s.value=e.key,h.value=e.value},K=async e=>{if(h.value)try{const l={key:e.key,value:h.value};await c(l),await g(),s.value="",h.value="",r.nk.success("保存成功")}catch(l){r.nk.error("保存失败")}else r.nk.warning("配置值不能为空")};return(e,n)=>{const r=(0,t.g2)("el-table-column"),i=(0,t.g2)("el-image"),c=(0,t.g2)("el-input"),k=(0,t.g2)("el-button"),g=(0,t.g2)("el-table"),F=(0,t.g2)("el-card"),E=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",d,[(0,t.bF)(F,{class:"box-card"},{header:(0,t.k6)((()=>n[3]||(n[3]=[(0,t.Lk)("div",{class:"card-header"},[(0,t.Lk)("span",null,"配置管理")],-1)]))),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(g,{data:l.value,style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(r,{prop:"name",label:"配置项",width:"180"}),(0,t.bF)(r,{prop:"value",label:"配置值"},{default:(0,t.k6)((({row:e})=>["store_photo"===e.key?((0,t.uX)(),(0,t.CE)("div",p,[(0,t.bF)(i,{src:w,"preview-src-list":[w],fit:"cover",class:"store-photo-preview","preview-teleported":""},{error:(0,t.k6)((()=>n[4]||(n[4]=[(0,t.Lk)("div",{class:"no-photo"}," 暂无照片 ",-1)]))),_:1},8,["preview-src-list"])])):((0,t.uX)(),(0,t.CE)(t.FK,{key:1},[s.value===e.key?((0,t.uX)(),(0,t.CE)(t.FK,{key:0},[_(e.key)?((0,t.uX)(),(0,t.Wv)(c,{key:0,modelValue:h.value,"onUpdate:modelValue":n[0]||(n[0]=e=>h.value=e),type:"textarea",rows:4,onKeyup:n[1]||(n[1]=(0,o.jR)((0,o.D$)((()=>{}),["stop"]),["enter"])),onBlur:l=>K(e),class:"full-width-input"},null,8,["modelValue","onBlur"])):((0,t.uX)(),(0,t.Wv)(c,{key:1,modelValue:h.value,"onUpdate:modelValue":n[2]||(n[2]=e=>h.value=e),onBlur:l=>K(e),onKeyup:(0,o.jR)((l=>K(e)),["enter"])},null,8,["modelValue","onBlur","onKeyup"]))],64)):((0,t.uX)(),(0,t.CE)("div",{key:1,onClick:l=>X(e),class:"config-value"},[_(e.key)?((0,t.uX)(),(0,t.CE)("div",y,(0,u.v_)(e.value),1)):((0,t.uX)(),(0,t.CE)("span",f,(0,u.v_)(e.value),1))],8,v))],64))])),_:1}),(0,t.bF)(r,{label:"操作",width:"180"},{default:(0,t.k6)((({row:e})=>["store_photo"===e.key?((0,t.uX)(),(0,t.CE)(t.FK,{key:0},[(0,t.Lk)("input",{type:"file",ref_key:"fileInput",ref:m,style:{display:"none"},accept:"image/*",onChange:b},null,544),(0,t.bF)(k,{type:"primary",size:"small",onClick:C},{default:(0,t.k6)((()=>n[5]||(n[5]=[(0,t.eW)("上传照片")]))),_:1})],64)):((0,t.uX)(),(0,t.CE)(t.FK,{key:1},[s.value!==e.key?((0,t.uX)(),(0,t.Wv)(k,{key:0,size:"small",type:"primary",onClick:l=>X(e)},{default:(0,t.k6)((()=>n[6]||(n[6]=[(0,t.eW)("编辑")]))),_:2},1032,["onClick"])):(0,t.Q3)("",!0),s.value===e.key?((0,t.uX)(),(0,t.Wv)(k,{key:1,size:"small",type:"success",onClick:l=>K(e)},{default:(0,t.k6)((()=>n[7]||(n[7]=[(0,t.eW)("保存")]))),_:2},1032,["onClick"])):(0,t.Q3)("",!0)],64))])),_:1})])),_:1},8,["data"])),[[E,a.value]])])),_:1})])}}}),m=a(9996);const w=(0,m.A)(h,[["__scopeId","data-v-b0bbde42"]]);var C=w}}]);
//# sourceMappingURL=997.e8fd0335.js.map