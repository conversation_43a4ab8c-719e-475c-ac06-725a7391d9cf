{"version": 3, "file": "js/997.e8fd0335.js", "mappings": "oPAaO,MAAMA,EAAaA,KACjBC,EAAAA,EAAAA,GAAQ,CACbC,IAAK,gBACLC,OAAQ,QAICC,EAAgBC,IACpBJ,EAAAA,EAAAA,GAAQ,CACbC,IAAK,gBACLC,OAAQ,OACRG,KAAMD,IAIGE,EAAoBC,IAC/B,MAAMC,EAAW,IAAIC,SAErB,OADAD,EAASE,OAAO,QAASH,IAClBP,EAAAA,EAAAA,GAAQ,CACbC,IAAK,oBACLC,OAAQ,OACRG,KAAMG,EACNG,QAAS,CACP,eAAgB,wBAElB,ECnCEC,EAAa,CAAEC,MAAO,iBACtBC,EAAa,CACjBC,IAAK,EACLF,MAAO,yBAEHG,EAAa,CAAC,WACdC,EAAa,CACjBF,IAAK,EACLF,MAAO,kBAEHK,EAAa,CAAEH,IAAK,GAQ1B,OAA4BI,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,eACRC,KAAAA,CAAMC,GCyFR,MAAMC,GAAUC,EAAAA,EAAAA,IAAc,IACxBC,GAAUD,EAAAA,EAAAA,KAAI,GACdE,GAAYF,EAAAA,EAAAA,IAAY,IACxBG,GAAeH,EAAAA,EAAAA,IAAI,IACnBI,GAAYJ,EAAAA,EAAAA,IAA6B,MAGzCK,EAAgBC,6CAGhBC,EAAmBA,KACvBH,EAAUI,OAAOC,OAAM,EAInBC,EAAmBC,UACvB,MAAMC,EAAQC,EAAMC,OACpB,IAAKF,EAAMG,OAAOC,OAAQ,OAE1B,MAAMC,EAAOL,EAAMG,MAAM,GAGnBG,EAAUD,EAAKE,KAAKC,WAAW,UAC/BC,EAASJ,EAAKK,KAAO,KAAO,KAAO,EAEzC,GAAKJ,EAIL,GAAKG,EAKL,IACEpB,EAAQO,OAAQ,QACV1B,EAAiBmC,GACvBM,EAAAA,GAAUC,QAAQ,QAElBC,OAAOC,SAASC,QAClB,CAAE,MAAOC,GACPL,EAAAA,GAAUK,MAAM,QAChBC,QAAQD,MAAM,QAASA,EACzB,CAAE,QACA3B,EAAQO,OAAQ,EAEhBI,EAAMJ,MAAQ,EAChB,MAjBEe,EAAAA,GAAUK,MAAM,sBAJhBL,EAAAA,GAAUK,MAAM,YAqBlB,EAGIE,EAAenB,UACnBV,EAAQO,OAAQ,EAChB,IACET,EAAQS,aAAejC,KAAcM,KAEhCkB,EAAQS,MAAMuB,MAAKC,GAAyB,gBAAfA,EAAOzC,OACvCQ,EAAQS,MAAMyB,KAAK,CACjB1C,IAAK,cACL2C,KAAM,OACN1B,MAAO,IAGb,CAAE,MAAOoB,GACPL,EAAAA,GAAUK,MAAM,UAChBC,QAAQD,MAAM,UAAWA,EAC3B,CAAE,QACA3B,EAAQO,OAAQ,CAClB,IAGF2B,EAAAA,EAAAA,IAAUL,GAGV,MAAMM,EAAqB7C,GAElB,CAAC,YAAYwC,MAAKM,GACvB9C,EAAI+C,cAAcC,SAASF,KAIzBG,EAAcC,IAClBvC,EAAUM,MAAQiC,EAAIlD,IACtBY,EAAaK,MAAQiC,EAAIjC,KAAI,EAGzBkC,EAAa/B,UACjB,GAAKR,EAAaK,MAKlB,IACE,MAAMmC,EAAmC,CACvCpD,IAAKkD,EAAIlD,IACTiB,MAAOL,EAAaK,aAEhB7B,EAAagE,SACbb,IACN5B,EAAUM,MAAQ,GAClBL,EAAaK,MAAQ,GACrBe,EAAAA,GAAUC,QAAQ,OACpB,CAAE,MAAOI,GACPL,EAAAA,GAAUK,MAAM,OAClB,MAhBEL,EAAAA,GAAUqB,QAAQ,UAgBpB,EDpFF,MAAO,CAACC,EAAUC,KAChB,MAAMC,GAA6BC,EAAAA,EAAAA,IAAkB,mBAC/CC,GAAsBD,EAAAA,EAAAA,IAAkB,YACxCE,GAAsBF,EAAAA,EAAAA,IAAkB,YACxCG,GAAuBH,EAAAA,EAAAA,IAAkB,aACzCI,GAAsBJ,EAAAA,EAAAA,IAAkB,YACxCK,GAAqBL,EAAAA,EAAAA,IAAkB,WACvCM,GAAqBC,EAAAA,EAAAA,IAAkB,WAE7C,OAAQC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOrE,EAAY,EAC3DsE,EAAAA,EAAAA,IAAaL,EAAoB,CAAEhE,MAAO,YAAc,CACtDsE,QAAQC,EAAAA,EAAAA,KAAS,IAAMd,EAAO,KAAOA,EAAO,GAAK,EAC/Ce,EAAAA,EAAAA,IAAoB,MAAO,CAAExE,MAAO,eAAiB,EACnDwE,EAAAA,EAAAA,IAAoB,OAAQ,KAAM,UAChC,OAENC,SAASF,EAAAA,EAAAA,KAAS,IAAM,EACtBG,EAAAA,EAAAA,MAAiBP,EAAAA,EAAAA,OAAcQ,EAAAA,EAAAA,IAAaZ,EAAqB,CAC/DvE,KAAMkB,EAAQS,MACdyD,MAAO,CAAC,MAAQ,SACf,CACDH,SAASF,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAaX,EAA4B,CACvCmB,KAAM,OACNC,MAAO,MACPC,MAAO,SAETV,EAAAA,EAAAA,IAAaX,EAA4B,CACvCmB,KAAM,QACNC,MAAO,OACN,CACDL,SAASF,EAAAA,EAAAA,KAAS,EAAGnB,SAAU,CAChB,gBAAZA,EAAIlD,MACAiE,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOnE,EAAY,EACpDoE,EAAAA,EAAAA,IAAaT,EAAqB,CAChCoB,IAAKhE,EACL,mBAAoB,CAACA,GACrBiE,IAAK,QACLjF,MAAO,sBACP,qBAAsB,IACrB,CACDuC,OAAOgC,EAAAA,EAAAA,KAAS,IAAMd,EAAO,KAAOA,EAAO,GAAK,EAC9Ce,EAAAA,EAAAA,IAAoB,MAAO,CAAExE,MAAO,YAAc,UAAW,OAE/DkF,EAAG,GACF,EAAG,CAAC,0BAERf,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoBe,EAAAA,GAAW,CAAEjF,IAAK,GAAK,CACvDW,EAAUM,QAAUiC,EAAIlD,MACpBiE,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoBe,EAAAA,GAAW,CAAEjF,IAAK,GAAK,CACvD6C,EAAkBK,EAAIlD,OAClBiE,EAAAA,EAAAA,OAAcQ,EAAAA,EAAAA,IAAad,EAAqB,CAC/C3D,IAAK,EACLkF,WAAYtE,EAAaK,MACzB,sBAAuBsC,EAAO,KAAOA,EAAO,GAAM4B,GAAkBvE,EAAcK,MAAQkE,GAC1FvD,KAAM,WACNwD,KAAM,EACNC,QAAS9B,EAAO,KAAOA,EAAO,IAAK+B,EAAAA,EAAAA,KAAUC,EAAAA,EAAAA,KAAe,QAAU,CAAC,SAAU,CAAC,WAClFC,OAASL,GAAiBhC,EAAWD,GACrCpD,MAAO,oBACN,KAAM,EAAG,CAAC,aAAc,cAC1BmE,EAAAA,EAAAA,OAAcQ,EAAAA,EAAAA,IAAad,EAAqB,CAC/C3D,IAAK,EACLkF,WAAYtE,EAAaK,MACzB,sBAAuBsC,EAAO,KAAOA,EAAO,GAAM4B,GAAkBvE,EAAcK,MAAQkE,GAC1FK,OAASL,GAAiBhC,EAAWD,GACrCmC,SAASC,EAAAA,EAAAA,KAAWH,GAAiBhC,EAAWD,IAAO,CAAC,WACvD,KAAM,EAAG,CAAC,aAAc,SAAU,cACxC,OACFe,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO,CACxClE,IAAK,EACLyF,QAAUN,GAAiBlC,EAAWC,GACtCpD,MAAO,gBACN,CACA+C,EAAkBK,EAAIlD,OAClBiE,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOhE,GAAYwF,EAAAA,EAAAA,IAAiBxC,EAAIjC,OAAQ,MAClFgD,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,OAAQ/D,GAAYuF,EAAAA,EAAAA,IAAiBxC,EAAIjC,OAAQ,KACvF,EAAGhB,KACT,QAET+E,EAAG,KAELb,EAAAA,EAAAA,IAAaX,EAA4B,CACvCoB,MAAO,KACPC,MAAO,OACN,CACDN,SAASF,EAAAA,EAAAA,KAAS,EAAGnB,SAAU,CAChB,gBAAZA,EAAIlD,MACAiE,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoBe,EAAAA,GAAW,CAAEjF,IAAK,GAAK,EACxDsE,EAAAA,EAAAA,IAAoB,QAAS,CAC3B1C,KAAM,OACN+D,QAAS,YACTlF,IAAKI,EACL6D,MAAO,CAAC,QAAU,QAClBkB,OAAQ,UACRC,SAAU1E,GACT,KAAM,MACTgD,EAAAA,EAAAA,IAAaP,EAAsB,CACjChC,KAAM,UACNG,KAAM,QACN0D,QAASzE,GACR,CACDuD,SAASF,EAAAA,EAAAA,KAAS,IAAMd,EAAO,KAAOA,EAAO,GAAK,EAChDuC,EAAAA,EAAAA,IAAiB,YAEnBd,EAAG,KAEJ,OACFf,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoBe,EAAAA,GAAW,CAAEjF,IAAK,GAAK,CACvDW,EAAUM,QAAUiC,EAAIlD,MACpBiE,EAAAA,EAAAA,OAAcQ,EAAAA,EAAAA,IAAab,EAAsB,CAChD5D,IAAK,EACL+B,KAAM,QACNH,KAAM,UACN6D,QAAUN,GAAiBlC,EAAWC,IACrC,CACDqB,SAASF,EAAAA,EAAAA,KAAS,IAAMd,EAAO,KAAOA,EAAO,GAAK,EAChDuC,EAAAA,EAAAA,IAAiB,UAEnBd,EAAG,GACF,KAAM,CAAC,cACVe,EAAAA,EAAAA,IAAoB,IAAI,GAC3BpF,EAAUM,QAAUiC,EAAIlD,MACpBiE,EAAAA,EAAAA,OAAcQ,EAAAA,EAAAA,IAAab,EAAsB,CAChD5D,IAAK,EACL+B,KAAM,QACNH,KAAM,UACN6D,QAAUN,GAAiBhC,EAAWD,IACrC,CACDqB,SAASF,EAAAA,EAAAA,KAAS,IAAMd,EAAO,KAAOA,EAAO,GAAK,EAChDuC,EAAAA,EAAAA,IAAiB,UAEnBd,EAAG,GACF,KAAM,CAAC,cACVe,EAAAA,EAAAA,IAAoB,IAAI,IAC3B,QAETf,EAAG,OAGPA,EAAG,GACF,EAAG,CAAC,UAAW,CAChB,CAACjB,EAAoBrD,EAAQO,YAGjC+D,EAAG,KAEL,CAEJ,I,UElRA,MAAMgB,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://admin-web/./src/api/config.ts", "webpack://admin-web/./src/views/settings/SettingsView.vue?439d", "webpack://admin-web/./src/views/settings/SettingsView.vue", "webpack://admin-web/./src/views/settings/SettingsView.vue?7296"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\nexport interface Config {\r\n  key: string\r\n  name: string\r\n  value: string\r\n}\r\n\r\nexport interface ConfigUpdateParams {\r\n  key: string\r\n  value: string\r\n}\r\n\r\nexport const getConfigs = () => {\r\n  return request({\r\n    url: '/queryConfigs',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport const modifyConfig = (configVO: ConfigUpdateParams) => {\r\n  return request({\r\n    url: '/modifyConfig',\r\n    method: 'post',\r\n    data: configVO\r\n  })\r\n} \r\n\r\nexport const uploadStorePhoto = (photo: File) => {\r\n  const formData = new FormData()\r\n  formData.append('photo', photo)\r\n  return request({\r\n    url: '/uploadStorePhoto',\r\n    method: 'post',\r\n    data: formData,\r\n    headers: {\r\n      'Content-Type': 'multipart/form-data'\r\n    }\r\n  })\r\n}\r\n\r\nexport const storePhoto = () => {\r\n  return request({\r\n    url: '/storePhoto',\r\n    method: 'get'\r\n  })\r\n}\r\n", "import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, withModifiers as _withModifiers, withKeys as _withKeys, createBlock as _createBlock, Fragment as _Fragment, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\"\n\nconst _hoisted_1 = { class: \"settings-view\" }\nconst _hoisted_2 = {\n  key: 0,\n  class: \"store-photo-container\"\n}\nconst _hoisted_3 = [\"onClick\"]\nconst _hoisted_4 = {\n  key: 0,\n  class: \"multiline-text\"\n}\nconst _hoisted_5 = { key: 1 }\n\nimport { ref, onMounted } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport { getConfigs, modifyConfig, type ConfigUpdate<PERSON>arams, uploadStorePhoto } from '@/api/config'\nimport type { Config } from '@/api/config'\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'SettingsView',\n  setup(__props) {\n\nconst configs = ref<Config[]>([])\nconst loading = ref(false)\nconst editingId = ref<string>('')\nconst editingValue = ref('')\nconst fileInput = ref<HTMLInputElement | null>(null)\n\n// 使用固定的图片URL\nconst storePhotoUrl = process.env.VUE_APP_API_BASE_URL + \"/storePhoto\"\n\n// 触发文件选择\nconst triggerFileInput = () => {\n  fileInput.value?.click()\n}\n\n// 处理文件选择\nconst handleFileChange = async (event: Event) => {\n  const input = event.target as HTMLInputElement\n  if (!input.files?.length) return\n\n  const file = input.files[0]\n  \n  // 验证文件类型和大小\n  const isImage = file.type.startsWith('image/')\n  const isLt2M = file.size / 1024 / 1024 < 2\n\n  if (!isImage) {\n    ElMessage.error('只能上传图片文件!')\n    return\n  }\n  if (!isLt2M) {\n    ElMessage.error('图片大小不能超过 2MB!')\n    return\n  }\n\n  try {\n    loading.value = true\n    await uploadStorePhoto(file)\n    ElMessage.success('上传成功')\n    // 上传成功后刷新页面以显示新图片\n    window.location.reload()\n  } catch (error) {\n    ElMessage.error('上传失败')\n    console.error('上传失败:', error)\n  } finally {\n    loading.value = false\n    // 清空文件输入框，允许重复选择同一文件\n    input.value = ''\n  }\n}\n\nconst fetchConfigs = async () => {\n  loading.value = true\n  try {\n    configs.value = (await getConfigs()).data\n    // 确保门店照片配置项存在\n    if (!configs.value.some(config => config.key === 'store_photo')) {\n      configs.value.push({\n        key: 'store_photo',\n        name: '门店照片',\n        value: ''\n      })\n    }\n  } catch (error) {\n    ElMessage.error('获取配置失败')\n    console.error('获取配置失败:', error)\n  } finally {\n    loading.value = false\n  }\n}\n\nonMounted(fetchConfigs)\n\n// 判断配置项是否为多行内容\nconst isMultilineConfig = (key: string) => {\n  // 根据键名判断是否为多行配置，如公告、协议等\n  return ['announce'].some(k => \n    key.toLowerCase().includes(k)\n  )\n}\n\nconst handleEdit = (row: Config) => {\n  editingId.value = row.key\n  editingValue.value = row.value\n}\n\nconst handleSave = async (row: Config) => {\n  if (!editingValue.value) {\n    ElMessage.warning('配置值不能为空')\n    return\n  }\n\n  try {\n    const updateParams: ConfigUpdateParams = {\n      key: row.key,\n      value: editingValue.value\n    }\n    await modifyConfig(updateParams)\n    await fetchConfigs()\n    editingId.value = ''\n    editingValue.value = ''\n    ElMessage.success('保存成功')\n  } catch (error) {\n    ElMessage.error('保存失败')\n  }\n}\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_el_image = _resolveComponent(\"el-image\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n  const _component_el_card = _resolveComponent(\"el-card\")!\n  const _directive_loading = _resolveDirective(\"loading\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createVNode(_component_el_card, { class: \"box-card\" }, {\n      header: _withCtx(() => _cache[3] || (_cache[3] = [\n        _createElementVNode(\"div\", { class: \"card-header\" }, [\n          _createElementVNode(\"span\", null, \"配置管理\")\n        ], -1)\n      ])),\n      default: _withCtx(() => [\n        _withDirectives((_openBlock(), _createBlock(_component_el_table, {\n          data: configs.value,\n          style: {\"width\":\"100%\"}\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_component_el_table_column, {\n              prop: \"name\",\n              label: \"配置项\",\n              width: \"180\"\n            }),\n            _createVNode(_component_el_table_column, {\n              prop: \"value\",\n              label: \"配置值\"\n            }, {\n              default: _withCtx(({ row }) => [\n                (row.key === 'store_photo')\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [\n                      _createVNode(_component_el_image, {\n                        src: storePhotoUrl,\n                        \"preview-src-list\": [storePhotoUrl],\n                        fit: \"cover\",\n                        class: \"store-photo-preview\",\n                        \"preview-teleported\": \"\"\n                      }, {\n                        error: _withCtx(() => _cache[4] || (_cache[4] = [\n                          _createElementVNode(\"div\", { class: \"no-photo\" }, \" 暂无照片 \", -1)\n                        ])),\n                        _: 1\n                      }, 8, [\"preview-src-list\"])\n                    ]))\n                  : (_openBlock(), _createElementBlock(_Fragment, { key: 1 }, [\n                      (editingId.value === row.key)\n                        ? (_openBlock(), _createElementBlock(_Fragment, { key: 0 }, [\n                            (isMultilineConfig(row.key))\n                              ? (_openBlock(), _createBlock(_component_el_input, {\n                                  key: 0,\n                                  modelValue: editingValue.value,\n                                  \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((editingValue).value = $event)),\n                                  type: \"textarea\",\n                                  rows: 4,\n                                  onKeyup: _cache[1] || (_cache[1] = _withKeys(_withModifiers(() => {}, [\"stop\"]), [\"enter\"])),\n                                  onBlur: ($event: any) => (handleSave(row)),\n                                  class: \"full-width-input\"\n                                }, null, 8, [\"modelValue\", \"onBlur\"]))\n                              : (_openBlock(), _createBlock(_component_el_input, {\n                                  key: 1,\n                                  modelValue: editingValue.value,\n                                  \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((editingValue).value = $event)),\n                                  onBlur: ($event: any) => (handleSave(row)),\n                                  onKeyup: _withKeys(($event: any) => (handleSave(row)), [\"enter\"])\n                                }, null, 8, [\"modelValue\", \"onBlur\", \"onKeyup\"]))\n                          ], 64))\n                        : (_openBlock(), _createElementBlock(\"div\", {\n                            key: 1,\n                            onClick: ($event: any) => (handleEdit(row)),\n                            class: \"config-value\"\n                          }, [\n                            (isMultilineConfig(row.key))\n                              ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, _toDisplayString(row.value), 1))\n                              : (_openBlock(), _createElementBlock(\"span\", _hoisted_5, _toDisplayString(row.value), 1))\n                          ], 8, _hoisted_3))\n                    ], 64))\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"操作\",\n              width: \"180\"\n            }, {\n              default: _withCtx(({ row }) => [\n                (row.key === 'store_photo')\n                  ? (_openBlock(), _createElementBlock(_Fragment, { key: 0 }, [\n                      _createElementVNode(\"input\", {\n                        type: \"file\",\n                        ref_key: \"fileInput\",\n                        ref: fileInput,\n                        style: {\"display\":\"none\"},\n                        accept: \"image/*\",\n                        onChange: handleFileChange\n                      }, null, 544),\n                      _createVNode(_component_el_button, {\n                        type: \"primary\",\n                        size: \"small\",\n                        onClick: triggerFileInput\n                      }, {\n                        default: _withCtx(() => _cache[5] || (_cache[5] = [\n                          _createTextVNode(\"上传照片\")\n                        ])),\n                        _: 1\n                      })\n                    ], 64))\n                  : (_openBlock(), _createElementBlock(_Fragment, { key: 1 }, [\n                      (editingId.value !== row.key)\n                        ? (_openBlock(), _createBlock(_component_el_button, {\n                            key: 0,\n                            size: \"small\",\n                            type: \"primary\",\n                            onClick: ($event: any) => (handleEdit(row))\n                          }, {\n                            default: _withCtx(() => _cache[6] || (_cache[6] = [\n                              _createTextVNode(\"编辑\")\n                            ])),\n                            _: 2\n                          }, 1032, [\"onClick\"]))\n                        : _createCommentVNode(\"\", true),\n                      (editingId.value === row.key)\n                        ? (_openBlock(), _createBlock(_component_el_button, {\n                            key: 1,\n                            size: \"small\",\n                            type: \"success\",\n                            onClick: ($event: any) => (handleSave(row))\n                          }, {\n                            default: _withCtx(() => _cache[7] || (_cache[7] = [\n                              _createTextVNode(\"保存\")\n                            ])),\n                            _: 2\n                          }, 1032, [\"onClick\"]))\n                        : _createCommentVNode(\"\", true)\n                    ], 64))\n              ]),\n              _: 1\n            })\n          ]),\n          _: 1\n        }, 8, [\"data\"])), [\n          [_directive_loading, loading.value]\n        ])\n      ]),\n      _: 1\n    })\n  ]))\n}\n}\n\n})", "<template>\n  <div class=\"settings-view\">\n    <el-card class=\"box-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <span>配置管理</span>\n        </div>\n      </template>\n      \n      <el-table v-loading=\"loading\" :data=\"configs\" style=\"width: 100%\">\n        <el-table-column prop=\"name\" label=\"配置项\" width=\"180\" />\n        <el-table-column prop=\"value\" label=\"配置值\">\n          <template #default=\"{ row }\">\n            <!-- 门店照片特殊处理 -->\n            <template v-if=\"row.key === 'store_photo'\">\n              <div class=\"store-photo-container\">\n                <el-image\n                  :src=\"storePhotoUrl\"\n                  :preview-src-list=\"[storePhotoUrl]\"\n                  fit=\"cover\"\n                  class=\"store-photo-preview\"\n                  preview-teleported\n                >\n                  <template #error>\n                    <div class=\"no-photo\">\n                      暂无照片\n                    </div>\n                  </template>\n                </el-image>\n              </div>\n            </template>\n            \n            <!-- 其他配置项的处理保持不变 -->\n            <template v-else>\n              <!-- 编辑状态 -->\n              <template v-if=\"editingId === row.key\">\n                <!-- 使用文本域处理公告等多行内容 -->\n                <el-input\n                  v-if=\"isMultilineConfig(row.key)\"\n                  v-model=\"editingValue\"\n                  type=\"textarea\"\n                  :rows=\"4\"\n                  @keyup.enter.stop\n                  @blur=\"handleSave(row)\"\n                  class=\"full-width-input\"\n                />\n                <!-- 普通输入框处理单行内容 -->\n                <el-input\n                  v-else\n                  v-model=\"editingValue\"\n                  @blur=\"handleSave(row)\"\n                  @keyup.enter=\"handleSave(row)\"\n                />\n              </template>\n              \n              <!-- 显示状态 -->\n              <div v-else @click=\"handleEdit(row)\" class=\"config-value\">\n                <!-- 对于多行内容，使用 white-space: pre-wrap 保留换行 -->\n                <div v-if=\"isMultilineConfig(row.key)\" class=\"multiline-text\">\n                  {{ row.value }}\n                </div>\n                <span v-else>{{ row.value }}</span>\n              </div>\n            </template>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" width=\"180\">\n          <template #default=\"{ row }\">\n            <!-- 门店照片的上传按钮 -->\n            <template v-if=\"row.key === 'store_photo'\">\n              <input\n                type=\"file\"\n                ref=\"fileInput\"\n                style=\"display: none\"\n                accept=\"image/*\"\n                @change=\"handleFileChange\"\n              />\n              <el-button\n                type=\"primary\"\n                size=\"small\"\n                @click=\"triggerFileInput\"\n              >上传照片</el-button>\n            </template>\n            \n            <!-- 其他配置项的操作按钮 -->\n            <template v-else>\n              <el-button\n                size=\"small\"\n                type=\"primary\"\n                @click=\"handleEdit(row)\"\n                v-if=\"editingId !== row.key\"\n              >编辑</el-button>\n              <el-button\n                size=\"small\"\n                type=\"success\"\n                @click=\"handleSave(row)\"\n                v-if=\"editingId === row.key\"\n              >保存</el-button>\n            </template>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-card>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport { getConfigs, modifyConfig, type ConfigUpdateParams, uploadStorePhoto } from '@/api/config'\nimport type { Config } from '@/api/config'\n\nconst configs = ref<Config[]>([])\nconst loading = ref(false)\nconst editingId = ref<string>('')\nconst editingValue = ref('')\nconst fileInput = ref<HTMLInputElement | null>(null)\n\n// 使用固定的图片URL\nconst storePhotoUrl = process.env.VUE_APP_API_BASE_URL + \"/storePhoto\"\n\n// 触发文件选择\nconst triggerFileInput = () => {\n  fileInput.value?.click()\n}\n\n// 处理文件选择\nconst handleFileChange = async (event: Event) => {\n  const input = event.target as HTMLInputElement\n  if (!input.files?.length) return\n\n  const file = input.files[0]\n  \n  // 验证文件类型和大小\n  const isImage = file.type.startsWith('image/')\n  const isLt2M = file.size / 1024 / 1024 < 2\n\n  if (!isImage) {\n    ElMessage.error('只能上传图片文件!')\n    return\n  }\n  if (!isLt2M) {\n    ElMessage.error('图片大小不能超过 2MB!')\n    return\n  }\n\n  try {\n    loading.value = true\n    await uploadStorePhoto(file)\n    ElMessage.success('上传成功')\n    // 上传成功后刷新页面以显示新图片\n    window.location.reload()\n  } catch (error) {\n    ElMessage.error('上传失败')\n    console.error('上传失败:', error)\n  } finally {\n    loading.value = false\n    // 清空文件输入框，允许重复选择同一文件\n    input.value = ''\n  }\n}\n\nconst fetchConfigs = async () => {\n  loading.value = true\n  try {\n    configs.value = (await getConfigs()).data\n    // 确保门店照片配置项存在\n    if (!configs.value.some(config => config.key === 'store_photo')) {\n      configs.value.push({\n        key: 'store_photo',\n        name: '门店照片',\n        value: ''\n      })\n    }\n  } catch (error) {\n    ElMessage.error('获取配置失败')\n    console.error('获取配置失败:', error)\n  } finally {\n    loading.value = false\n  }\n}\n\nonMounted(fetchConfigs)\n\n// 判断配置项是否为多行内容\nconst isMultilineConfig = (key: string) => {\n  // 根据键名判断是否为多行配置，如公告、协议等\n  return ['announce'].some(k => \n    key.toLowerCase().includes(k)\n  )\n}\n\nconst handleEdit = (row: Config) => {\n  editingId.value = row.key\n  editingValue.value = row.value\n}\n\nconst handleSave = async (row: Config) => {\n  if (!editingValue.value) {\n    ElMessage.warning('配置值不能为空')\n    return\n  }\n\n  try {\n    const updateParams: ConfigUpdateParams = {\n      key: row.key,\n      value: editingValue.value\n    }\n    await modifyConfig(updateParams)\n    await fetchConfigs()\n    editingId.value = ''\n    editingValue.value = ''\n    ElMessage.success('保存成功')\n  } catch (error) {\n    ElMessage.error('保存失败')\n  }\n}\n</script>\n\n<style scoped>\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.el-table :deep(.cell) {\n  line-height: 32px;\n}\n\n.el-table :deep(.el-input) {\n  margin: -6px 0;\n}\n\n.full-width-input {\n  width: 100%;\n}\n\n/* 多行文本样式 */\n.multiline-text {\n  white-space: pre-wrap;\n  word-break: break-word;\n  line-height: 1.5;\n}\n\n/* 配置值容器 */\n.config-value {\n  cursor: pointer;\n  min-height: 20px;\n}\n\n/* 门店照片相关样式 */\n.store-photo-container {\n  display: flex;\n  align-items: center;\n  min-height: 100px;\n}\n\n.store-photo-preview {\n  width: 100px;\n  height: 100px;\n  border-radius: 4px;\n  cursor: pointer;\n  object-fit: cover;\n  border: 1px solid #dcdfe6;\n}\n\n.no-photo {\n  width: 100px;\n  height: 100px;\n  border-radius: 4px;\n  border: 1px dashed #dcdfe6;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 调整操作列宽度 */\n.el-table :deep(.el-table__cell) {\n  padding: 8px 0;\n}\n</style> ", "import script from \"./SettingsView.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./SettingsView.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./SettingsView.vue?vue&type=style&index=0&id=b0bbde42&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/.pnpm/vue-loader@17.4.2_@vue+compiler-sfc@3.5.13_vue@3.5.13_typescript@5.8.3__webpack@5.98.0/node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-b0bbde42\"]])\n\nexport default __exports__"], "names": ["getConfigs", "request", "url", "method", "modifyConfig", "configVO", "data", "uploadStorePhoto", "photo", "formData", "FormData", "append", "headers", "_hoisted_1", "class", "_hoisted_2", "key", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_defineComponent", "__name", "setup", "__props", "configs", "ref", "loading", "editingId", "editingValue", "fileInput", "storePhotoUrl", "process", "triggerFileInput", "value", "click", "handleFileChange", "async", "input", "event", "target", "files", "length", "file", "isImage", "type", "startsWith", "isLt2M", "size", "ElMessage", "success", "window", "location", "reload", "error", "console", "fetchConfigs", "some", "config", "push", "name", "onMounted", "isMultilineConfig", "k", "toLowerCase", "includes", "handleEdit", "row", "handleSave", "updateParams", "warning", "_ctx", "_cache", "_component_el_table_column", "_resolveComponent", "_component_el_image", "_component_el_input", "_component_el_button", "_component_el_table", "_component_el_card", "_directive_loading", "_resolveDirective", "_openBlock", "_createElementBlock", "_createVNode", "header", "_withCtx", "_createElementVNode", "default", "_withDirectives", "_createBlock", "style", "prop", "label", "width", "src", "fit", "_", "_Fragment", "modelValue", "$event", "rows", "onKeyup", "_with<PERSON><PERSON><PERSON>", "_withModifiers", "onBlur", "onClick", "_toDisplayString", "ref_key", "accept", "onChange", "_createTextVNode", "_createCommentVNode", "__exports__"], "sourceRoot": ""}