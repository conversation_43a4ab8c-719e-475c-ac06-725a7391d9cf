{"version": 3, "file": "js/427.207beb5c.js", "mappings": "2TAGA,MAAMA,EAAa,CAAEC,MAAO,gBACtBC,EAAa,CAAED,MAAO,eACtBE,EAAa,CAAEF,MAAO,qBACtBG,EAAa,CAAEH,MAAO,gBACtBI,EAAa,CAAEJ,MAAO,kBACtBK,EAAa,CAAEL,MAAO,iBA8B5B,OAA4BM,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,cACRC,KAAAA,CAAMC,GC0QN,MAAMC,GAAmBC,EAAAA,EAAAA,IAAiB,IACpCC,GAAoBD,EAAAA,EAAAA,IAAe,IACnCE,GAAWF,EAAAA,EAAAA,IAAe,IAC1BG,GAAaH,EAAAA,EAAAA,IAAgB,IAC7BI,GAASJ,EAAAA,EAAAA,IAAa,IACtBK,GAAUL,EAAAA,EAAAA,KAAI,GACdM,GAAgBN,EAAAA,EAAAA,KAAI,GACpBO,GAAcP,EAAAA,EAAAA,IAAI,IAClBQ,GAAUR,EAAAA,EAAAA,MAGVS,GAAcT,EAAAA,EAAAA,IAAc,IAC5BU,GAAkBV,EAAAA,EAAAA,KAAI,GACtBW,GAAgBX,EAAAA,EAAAA,IAAI,IACpBY,GAAcZ,EAAAA,EAAAA,MAEda,GAAOb,EAAAA,EAAAA,IAAoC,CAC/Cc,eAAWC,EACXC,MAAO,EACPC,SAAU,EACVC,OAAQ,GACRC,UAAW,EACXC,KAAM,KAGFC,GAAoBrB,EAAAA,EAAAA,MACpBsB,GAAqBtB,EAAAA,EAAAA,MACrBuB,GAAkBvB,EAAAA,EAAAA,MAGlBwB,GAAexB,EAAAA,EAAAA,IAAI,IACnByB,GAAkBzB,EAAAA,EAAAA,IAAI,IAGtB0B,GAAgB1B,EAAAA,EAAAA,IAAoB,IAGpC2B,GAAa3B,EAAAA,EAAAA,IAAI,CACrB4B,KAAM,GACNC,MAAO,KAIHC,GAAqBC,EAAAA,EAAAA,KAAS,IAC7BV,EAAkBW,MAChB7B,EAAW6B,MAAMC,QAAOC,GAAKA,EAAEC,YAAcd,EAAkBW,QADjC,KAIjCI,GAAiBL,EAAAA,EAAAA,KAAS,IACzBT,EAAmBU,MACjB5B,EAAO4B,MAAMC,QAAOI,GAAKA,EAAEC,aAAehB,EAAmBU,QAD9B,KAIlCO,GAA4BR,EAAAA,EAAAA,KAAS,IACpCR,EAAgBS,MACd/B,EAAkB+B,MAAMC,QAAOO,GAAKA,EAAEC,UAAYlB,EAAgBS,QADtC,KAK/BU,GAAmBX,EAAAA,EAAAA,KAAS,KAChC,IAAIY,EAAS,IAAI5C,EAAiBiC,OAuBlC,GApB2B,KAAvBR,EAAaQ,QACfW,EAASA,EAAOV,QAAOW,IACrB,MAAMC,EAAWC,IAAMF,EAAQ1B,QAAQ6B,QAAQD,OAC/C,MAA2B,WAAvBtB,EAAaQ,MACRa,EACyB,aAAvBrB,EAAaQ,QACda,CAEA,KAKgB,KAA1BpB,EAAgBO,QAClBW,EAASA,EAAOV,QAAOW,GACdI,EAAeJ,EAAQ9B,aAAeW,EAAgBO,SAK7DL,EAAWK,MAAMJ,MAAQD,EAAWK,MAAMH,MAAO,CACnD,MAAMD,EAAOD,EAAWK,MAAMJ,KACxBqB,EAAmC,cAA3BtB,EAAWK,MAAMH,MAE/Bc,EAAOO,MAAK,CAACC,EAAGd,KACd,IAAIe,EAAa,EACjB,GAAa,aAATxB,EACFwB,EAAaD,EAAElC,SAAWoB,EAAEpB,cACvB,GAAa,UAATW,EAAkB,CAC3B,MAAMyB,EAA4B,kBAAZF,EAAEnC,MAAqBsC,WAAWH,EAAEnC,OAASmC,EAAEnC,MAC/DuC,EAA4B,kBAAZlB,EAAErB,MAAqBsC,WAAWjB,EAAErB,OAASqB,EAAErB,MACrEoC,EAAaC,EAASE,CACxB,KAAoB,cAAT3B,EACTwB,EAAaD,EAAEhC,UAAYkB,EAAElB,UACX,WAATS,IACTwB,EAAaN,IAAMK,EAAEjC,QAAQsC,SAASV,IAAMT,EAAEnB,UAAY,EAAI,GAGhE,OAAO+B,EAAQG,GAAcA,CAAS,GAE1C,CAEA,OAAOT,CAAK,IAIRc,EAAsBA,KAC1BnC,EAAmBU,WAAQjB,EAC3BQ,EAAgBS,WAAQjB,EACxBF,EAAKmB,MAAMlB,eAAYC,CAAQ,EAG3B2C,EAAuBA,KAC3BnC,EAAgBS,WAAQjB,EACxBF,EAAKmB,MAAMlB,eAAYC,CAAQ,EAG3B4C,EAAoBA,KACxB9C,EAAKmB,MAAMlB,eAAYC,CAAQ,EAI3B6C,EAAeA,KACnBlD,EAAgBsB,OAAQ,GACxB6B,EAAAA,EAAAA,KAAS,KACPjD,EAAYoB,OAAO8B,OAAM,GACzB,EAGEC,EAAwBA,KACxBpD,EAAcqB,QACXvB,EAAYuB,MAAMgC,SAASrD,EAAcqB,SAC5CvB,EAAYuB,MAAMiC,KAAKtD,EAAcqB,OACrCkC,MAGJxD,EAAgBsB,OAAQ,EACxBrB,EAAcqB,MAAQ,EAAC,EAGnBmC,EAAkBC,IACtB3D,EAAYuB,MAAMqC,OAAO5D,EAAYuB,MAAMsC,QAAQF,GAAM,GACzDF,GAAe,EAGXA,EAAiBA,KACrBrD,EAAKmB,MAAMZ,KAAOX,EAAYuB,MAAMuC,KAAK,IAAI,EAGzCC,EAAWC,GACVA,EACEA,EAAWC,MAAM,KAAKzC,QAAOmC,GAAsB,KAAfA,EAAIO,SADvB,GAIpBC,EAAQ,CACZ9D,UAAW,CACT,CAAE+D,UAAU,EAAMC,QAAS,QAASC,QAAS,WAE/C/D,MAAO,CACL,CAAE6D,UAAU,EAAMC,QAAS,QAASC,QAAS,SAE/C9D,SAAU,CACR,CAAE4D,UAAU,EAAMC,QAAS,QAASC,QAAS,SAE/C7D,OAAQ,CACN,CAAE2D,UAAU,EAAMC,QAAS,SAAUC,QAAS,WAEhD5D,UAAW,CACT,CAAE0D,UAAU,EAAMC,QAAS,UAAWC,QAAS,UAI7CC,EAAeC,UACnB5E,EAAQ2B,OAAQ,EAChB,IACE,MAAOkD,EAAqBC,EAAsBC,EAAaC,EAAeC,EAAWC,SAAmBC,QAAQC,IAAI,EACtHC,EAAAA,EAAAA,OACAC,EAAAA,EAAAA,OACAC,EAAAA,EAAAA,OACAC,EAAAA,EAAAA,OACAC,EAAAA,EAAAA,OACAC,EAAAA,EAAAA,QAEFhG,EAAiBiC,MAAQkD,EAAoBc,KAC7C/F,EAAkB+B,MAAQmD,EAAqBa,KAC/C9F,EAAS8B,MAAQoD,EAAYY,KAC7B7F,EAAW6B,MAAQqD,EAAcW,KACjC5F,EAAO4B,MAAQsD,EAAUU,KACzBtE,EAAcM,MAAQuD,EAAUS,KAAKC,KAAKC,IAAiB,IACtDA,EACHpF,UAAWqF,SAASC,OAAOF,EAAIpF,eAEnC,CAAE,MAAOuF,GACPC,EAAAA,GAAUD,MAAM,UAChBE,QAAQF,MAAM,UAAWA,EAC3B,CAAE,QACAhG,EAAQ2B,OAAQ,CAClB,IAGFwE,EAAAA,EAAAA,IAAUxB,GAEV,MAAMyB,EAAezF,IAEnB,MAAM0F,EAA4B,kBAAV1F,EAAqBsC,WAAWtC,GAASA,EAGjE,OAAI2F,MAAMD,GACD,QAGF,KAAKA,EAASE,QAAQ,IAAG,EAG5BC,EAAkB/F,IACtB,MAAM8B,EAAU3C,EAAkB+B,MAAM8E,MAAKtE,GAAKA,EAAEuE,IAAMjG,IAC1D,OAAO8B,GAASoE,MAAQ,GAAE,EAGtBhE,EAAkBlC,IACtB,MAAM8B,EAAU3C,EAAkB+B,MAAM8E,MAAKtE,GAAKA,EAAEuE,IAAMjG,IACpDmG,EAAU/G,EAAS8B,MAAM8E,MAAKI,GAAKA,EAAEH,IAAMnE,GAAST,YAC1D,OAAO8E,GAASD,MAAQ,GAAE,EAGtBG,GAAYA,KAChB5G,EAAYyB,MAAQ,OACpBnB,EAAKmB,MAAQ,CACXlB,eAAWC,EACXC,MAAO,EACPC,SAAU,EACVC,OAAQ4B,MAAQsE,OAAO,uBACvBjG,UAAW,EACXC,KAAM,IAERX,EAAYuB,MAAQ,GACpBX,EAAkBW,WAAQjB,EAC1BO,EAAmBU,WAAQjB,EAC3BQ,EAAgBS,WAAQjB,EACxBT,EAAc0B,OAAQ,CAAG,EAGrBqF,GAAcC,IAClB/G,EAAYyB,MAAQ,OACpB,MAAMY,EAAU3C,EAAkB+B,MAAM8E,MAAKtE,GAAKA,EAAEuE,IAAMO,EAAIxG,YAC9D,GAAI8B,EAAS,CACXrB,EAAgBS,MAAQY,EAAQH,QAChC,MAAM8E,EAAQnH,EAAO4B,MAAM8E,MAAKzE,GAAKA,EAAE0E,IAAMnE,EAAQH,UACrD,GAAI8E,EAAO,CACTjG,EAAmBU,MAAQuF,EAAMjF,WACjC,MAAMkF,EAAWrH,EAAW6B,MAAM8E,MAAK5E,GAAKA,EAAE6E,IAAMQ,EAAMjF,aACtDkF,IACFnG,EAAkBW,MAAQwF,EAASrF,UAEvC,CACF,CAEAtB,EAAKmB,MAAQ,CACX+E,GAAIO,EAAIP,GACRjG,UAAWwG,EAAIxG,UACfE,MAAOsG,EAAItG,MACXC,SAAUqG,EAAIrG,SACdC,OAAQoG,EAAIpG,OACZC,UAAWmG,EAAInG,UACfC,KAAMkG,EAAIlG,MAAQ,IAIpBX,EAAYuB,MAAQwC,EAAQ8C,EAAIlG,MAAQ,IAExCd,EAAc0B,OAAQ,CAAG,EAGrByF,GAAgBH,IACpBI,EAAAA,EAAaC,QACX,cACA,KACA,CACEC,kBAAmB,KACnBC,iBAAkB,KAClBC,KAAM,YAERC,MAAK9C,UACL,UACQ+C,EAAAA,EAAAA,IAAgBV,EAAIP,UACpB/B,IACNsB,EAAAA,GAAU2B,QAAQ,OACpB,CAAE,MAAO5B,GACPC,EAAAA,GAAUD,MAAM,OAClB,KACC6B,OAAM,QAEP,EAGEC,GAAelD,UACdzE,EAAQwB,aAEPxB,EAAQwB,MAAMoG,UAASnD,UAC3B,GAAIoD,EACF,IACMxH,EAAKmB,MAAM+E,SACPuB,EAAAA,EAAAA,IAAgBzH,EAAKmB,aAErBuG,EAAAA,EAAAA,IAAgB1H,EAAKmB,OAE7B1B,EAAc0B,OAAQ,QAChBgD,IACNsB,EAAAA,GAAU2B,QAAQpH,EAAKmB,MAAM+E,GAAK,OAAS,OAC7C,CAAE,MAAOV,GACPC,EAAAA,GAAUD,MAAMxF,EAAKmB,MAAM+E,GAAK,OAAS,OAC3C,CACF,GACA,EAGEyB,GAAoBA,KACpBhI,EAAQwB,OACVxB,EAAQwB,MAAMyG,cAEhBpH,EAAkBW,WAAQjB,EAC1BO,EAAmBU,WAAQjB,EAC3BQ,EAAgBS,WAAQjB,EACxBN,EAAYuB,MAAQ,GACpBtB,EAAgBsB,OAAQ,EACxBrB,EAAcqB,MAAQ,EAAC,EAInB0G,GAAiBxH,GACd4B,IAAM5B,GAAQ6B,QAAQD,OAAW,UAAY,SAGhD6F,GAAiBzH,GACd4B,IAAM5B,GAAQ6B,QAAQD,OAAW,KAAO,KAI3C8F,GAAmB9H,IACvB,MAAM+H,EAAQnH,EAAcM,MAAM8E,MAAKZ,GAAOA,EAAIpF,WAAaA,IAC/D,OAAK+H,EAEEC,gDAAsDD,GAAOE,SAD3D,IAAIC,IAAI,aAAyCC,IACiB,EAIvEC,GAAejE,MAAOkE,EAAwB7B,KAClD,IAEE,GAAI6B,EAAOC,KAAKC,KAAO,QAErB,YADA/C,EAAAA,GAAUD,MAAM,qBAIZiD,EAAAA,EAAAA,IAAgBhC,EAAIxG,UAAWqI,EAAOC,MAC5C9C,EAAAA,GAAU2B,QAAQ,cACZjD,GACR,CAAE,MAAOqB,GACPC,EAAAA,GAAUD,MAAM,QAChBE,QAAQF,MAAM,QAASA,EACzB,GAIIkD,GAAiBA,CAACpG,EAAcd,IAC7Bc,EAAElC,SAAWoB,EAAEpB,SAGlBuI,GAAcA,CAACrG,EAAcd,KAEjC,MAAMgB,EAA4B,kBAAZF,EAAEnC,MAAqBsC,WAAWH,EAAEnC,OAASmC,EAAEnC,MAC/DuC,EAA4B,kBAAZlB,EAAErB,MAAqBsC,WAAWjB,EAAErB,OAASqB,EAAErB,MACrE,OAAOqC,EAASE,CAAK,EAGjBkG,GAAkBA,CAACtG,EAAcd,IAC9Bc,EAAEhC,UAAYkB,EAAElB,UAGnBuI,GAAgBA,CAACvG,EAAcd,IAC5BS,IAAMK,EAAEjC,QAAQsC,SAASV,IAAMT,EAAEnB,UAAY,EAAI,EAIpDyI,GAAcC,IAClBjI,EAAWK,MAAMJ,KAAOgI,EAAOhI,KAC/BD,EAAWK,MAAMH,MAAQ+H,EAAO/H,KAAI,EDrQxC,MAAO,CAACgI,EAAUC,KAChB,MAAMC,GAAuBC,EAAAA,EAAAA,IAAkB,aACzCC,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCE,GAAuBF,EAAAA,EAAAA,IAAkB,aACzCG,GAA6BH,EAAAA,EAAAA,IAAkB,mBAC/CI,GAAsBJ,EAAAA,EAAAA,IAAkB,YACxCK,GAAoBL,EAAAA,EAAAA,IAAkB,UACtCM,GAAuBN,EAAAA,EAAAA,IAAkB,aACzCO,GAAsBP,EAAAA,EAAAA,IAAkB,YACxCQ,GAAqBR,EAAAA,EAAAA,IAAkB,WACvCS,GAA0BT,EAAAA,EAAAA,IAAkB,gBAC5CU,GAAsBV,EAAAA,EAAAA,IAAkB,YACxCW,IAA6BX,EAAAA,EAAAA,IAAkB,mBAC/CY,IAA4BZ,EAAAA,EAAAA,IAAkB,kBAC9Ca,IAAqBb,EAAAA,EAAAA,IAAkB,WACvCc,IAAuBd,EAAAA,EAAAA,IAAkB,aACzCe,IAAqBC,EAAAA,EAAAA,IAAkB,WAE7C,OAAQC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO9L,EAAY,EAC3D+L,EAAAA,EAAAA,IAAaX,EAAoB,CAAEnL,MAAO,YAAc,CACtD+L,QAAQC,EAAAA,EAAAA,KAAS,IAAM,EACrBC,EAAAA,EAAAA,IAAoB,MAAOhM,EAAY,CACrCwK,EAAO,MAAQA,EAAO,KAAMwB,EAAAA,EAAAA,IAAoB,OAAQ,KAAM,QAAS,KACvEA,EAAAA,EAAAA,IAAoB,MAAO/L,EAAY,EACrC+L,EAAAA,EAAAA,IAAoB,MAAO9L,EAAY,EACrC2L,EAAAA,EAAAA,IAAalB,EAAsB,CACjCsB,WAAY/J,EAAaQ,MACzB,sBAAuB8H,EAAO,KAAOA,EAAO,GAAM0B,GAAkBhK,EAAcQ,MAAQwJ,GAC1FC,YAAa,KACbpM,MAAO,6BACN,CACDqM,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAapB,EAAsB,CACjC4B,MAAO,KACP3J,MAAO,MAETmJ,EAAAA,EAAAA,IAAapB,EAAsB,CACjC4B,MAAO,KACP3J,MAAO,YAETmJ,EAAAA,EAAAA,IAAapB,EAAsB,CACjC4B,MAAO,KACP3J,MAAO,gBAGX4J,EAAG,GACF,EAAG,CAAC,gBACPT,EAAAA,EAAAA,IAAalB,EAAsB,CACjCsB,WAAY9J,EAAgBO,MAC5B,sBAAuB8H,EAAO,KAAOA,EAAO,GAAM0B,GAAkB/J,EAAiBO,MAAQwJ,GAC7FC,YAAa,KACbpM,MAAO,8BACN,CACDqM,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAapB,EAAsB,CACjC4B,MAAO,KACP3J,MAAO,OAERiJ,EAAAA,EAAAA,KAAW,IAAOC,EAAAA,EAAAA,IAAoBW,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAY5L,EAAS8B,OAAQiF,KAC3EgE,EAAAA,EAAAA,OAAcc,EAAAA,EAAAA,IAAahC,EAAsB,CACvDiC,IAAK/E,EAAQF,GACb4E,MAAO1E,EAAQD,KACfhF,MAAOiF,EAAQD,MACd,KAAM,EAAG,CAAC,QAAS,aACpB,SAEN4E,EAAG,GACF,EAAG,CAAC,kBAETT,EAAAA,EAAAA,IAAajB,EAAsB,CACjCpC,KAAM,UACNmE,QAAS9E,IACR,CACDuE,SAASL,EAAAA,EAAAA,KAAS,IAAMvB,EAAO,MAAQA,EAAO,IAAM,EAClDoC,EAAAA,EAAAA,IAAiB,YAEnBN,EAAG,WAKXF,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBc,EAAAA,EAAAA,MAAiBlB,EAAAA,EAAAA,OAAcc,EAAAA,EAAAA,IAAaxB,EAAqB,CAC/DvE,KAAMtD,EAAiBV,MACvBoK,MAAO,CAAC,MAAQ,QAChBC,aAAc1C,IACb,CACD+B,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAahB,EAA4B,CACvCvI,KAAM,KACN+J,MAAO,MACPW,MAAO,QAETnB,EAAAA,EAAAA,IAAahB,EAA4B,CACvCwB,MAAO,KACPW,MAAO,MACN,CACDZ,SAASL,EAAAA,EAAAA,KAAS,EAAG/D,SAAU,EAC7B6D,EAAAA,EAAAA,IAAaf,EAAqB,CAChCmC,IAAK3D,GAAgBtB,EAAIxG,WACzB,mBAAoB,CAAC8H,GAAgBtB,EAAIxG,YACzC0L,IAAK,QACLJ,MAAO,CAAC,MAAQ,OAAO,OAAS,QAChC,qBAAsB,IACrB,KAAM,EAAG,CAAC,MAAO,wBAEtBR,EAAG,KAELT,EAAAA,EAAAA,IAAahB,EAA4B,CACvCwB,MAAO,KACPW,MAAO,OACN,CACDZ,SAASL,EAAAA,EAAAA,KAAS,EAAG/D,SAAU,EAC7B6D,EAAAA,EAAAA,IAAad,EAAmB,CAC9BvC,KAAMY,GAAcpB,EAAIpG,SACvB,CACDwK,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBa,EAAAA,EAAAA,KAAiBO,EAAAA,EAAAA,IAAiB9D,GAAcrB,EAAIpG,SAAU,MAEhE0K,EAAG,GACF,KAAM,CAAC,YAEZA,EAAG,KAELT,EAAAA,EAAAA,IAAahB,EAA4B,CACvCwB,MAAO,OACPW,MAAO,OACN,CACDZ,SAASL,EAAAA,EAAAA,KAAS,EAAG/D,SAAU,EAC7B4E,EAAAA,EAAAA,KAAiBO,EAAAA,EAAAA,IAAiBzJ,EAAesE,EAAIxG,YAAa,MAEpE8K,EAAG,KAELT,EAAAA,EAAAA,IAAahB,EAA4B,CACvCvI,KAAM,YACN+J,MAAO,MACPW,MAAO,SAETnB,EAAAA,EAAAA,IAAahB,EAA4B,CACvCwB,MAAO,OACP,YAAa,OACZ,CACDD,SAASL,EAAAA,EAAAA,KAAS,EAAG/D,SAAU,EAC7B4E,EAAAA,EAAAA,KAAiBO,EAAAA,EAAAA,IAAiB5F,EAAeS,EAAIxG,YAAa,MAEpE8K,EAAG,KAELT,EAAAA,EAAAA,IAAahB,EAA4B,CACvCwB,MAAO,KACPW,MAAO,OACN,CACDZ,SAASL,EAAAA,EAAAA,KAAS,EAAG/D,SAAU,EAC7BgE,EAAAA,EAAAA,IAAoB,MAAO7L,EAAY,GACpCwL,EAAAA,EAAAA,KAAW,IAAOC,EAAAA,EAAAA,IAAoBW,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAYtH,EAAQ8C,EAAIlG,OAAO,CAACgD,EAAKsI,MACnFzB,EAAAA,EAAAA,OAAcc,EAAAA,EAAAA,IAAa1B,EAAmB,CACpD2B,IAAKU,EACLrD,KAAM,QACNhK,MAAO,YACN,CACDqM,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBa,EAAAA,EAAAA,KAAiBO,EAAAA,EAAAA,IAAiBrI,GAAM,MAE1CwH,EAAG,GACF,SACD,WAGRA,EAAG,KAELT,EAAAA,EAAAA,IAAahB,EAA4B,CACvCwB,MAAO,KACPW,MAAO,MACPK,SAAU,GACV,cAAepD,IACd,CACDmC,SAASL,EAAAA,EAAAA,KAAS,EAAG/D,SAAU,EAC7B4E,EAAAA,EAAAA,KAAiBO,EAAAA,EAAAA,IAAiBnF,EAAIrG,UAAW,MAEnD2K,EAAG,KAELT,EAAAA,EAAAA,IAAahB,EAA4B,CACvCwB,MAAO,MACPW,MAAO,MACPK,SAAU,GACV,cAAenD,IACd,CACDkC,SAASL,EAAAA,EAAAA,KAAS,EAAG/D,SAAU,EAC7B4E,EAAAA,EAAAA,KAAiBO,EAAAA,EAAAA,IAAiBhG,EAAYa,EAAItG,QAAS,MAE7D4K,EAAG,KAELT,EAAAA,EAAAA,IAAahB,EAA4B,CACvCwB,MAAO,OACPW,MAAO,MACPK,SAAU,GACV,cAAelD,IACd,CACDiC,SAASL,EAAAA,EAAAA,KAAS,EAAG/D,SAAU,EAC7B4E,EAAAA,EAAAA,KAAiBO,EAAAA,EAAAA,IAAiBnF,EAAInG,WAAY,MAEpDyK,EAAG,KAELT,EAAAA,EAAAA,IAAahB,EAA4B,CACvCwB,MAAO,OACPW,MAAO,MACPK,SAAU,GACV,cAAejD,IACd,CACDgC,SAASL,EAAAA,EAAAA,KAAS,EAAG/D,SAAU,EAC7B4E,EAAAA,EAAAA,KAAiBO,EAAAA,EAAAA,KAAiBG,EAAAA,EAAAA,IAAO9J,IAAP8J,CAActF,EAAIpG,QAAQkG,OAAO,wBAAyB,MAE9FwE,EAAG,KAELT,EAAAA,EAAAA,IAAahB,EAA4B,CACvCwB,MAAO,KACPW,MAAO,MACPO,MAAO,SACN,CACDnB,SAASL,EAAAA,EAAAA,KAAUyB,GAAU,EAC3B3B,EAAAA,EAAAA,IAAajB,EAAsB,CACjCb,KAAM,QACN4C,QAAUT,GAAiBnE,GAAWyF,EAAMxF,MAC3C,CACDoE,SAASL,EAAAA,EAAAA,KAAS,IAAMvB,EAAO,MAAQA,EAAO,IAAM,EAClDoC,EAAAA,EAAAA,IAAiB,UAEnBN,EAAG,GACF,KAAM,CAAC,aACVT,EAAAA,EAAAA,IAAab,EAAsB,CACjCjL,MAAO,gBACP,kBAAkB,EAClB,eAAiB8J,GAAWD,GAAaC,EAAQ2D,EAAMxF,KACvDyF,OAAQ,WACP,CACDrB,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAajB,EAAsB,CACjCb,KAAM,QACNvB,KAAM,WACL,CACD4D,SAASL,EAAAA,EAAAA,KAAS,IAAMvB,EAAO,MAAQA,EAAO,IAAM,EAClDoC,EAAAA,EAAAA,IAAiB,YAEnBN,EAAG,OAGPA,EAAG,GACF,KAAM,CAAC,kBACVT,EAAAA,EAAAA,IAAajB,EAAsB,CACjCb,KAAM,QACNvB,KAAM,SACNmE,QAAUT,GAAiB/D,GAAaqF,EAAMxF,MAC7C,CACDoE,SAASL,EAAAA,EAAAA,KAAS,IAAMvB,EAAO,MAAQA,EAAO,IAAM,EAClDoC,EAAAA,EAAAA,IAAiB,UAEnBN,EAAG,GACF,KAAM,CAAC,eAEZA,EAAG,OAGPA,EAAG,GACF,EAAG,CAAC,UAAW,CAChB,CAACb,GAAoB1K,EAAQ2B,YAGjC4J,EAAG,KAELT,EAAAA,EAAAA,IAAaL,GAAsB,CACjCkC,MAAOzM,EAAYyB,MACnBuJ,WAAYjL,EAAc0B,MAC1B,sBAAuB8H,EAAO,MAAQA,EAAO,IAAO0B,GAAkBlL,EAAe0B,MAAQwJ,GAC7Fc,MAAO,QACPW,QAASzE,IACR,CACD0E,QAAQ7B,EAAAA,EAAAA,KAAS,IAAM,EACrBC,EAAAA,EAAAA,IAAoB,OAAQ5L,EAAY,EACtCyL,EAAAA,EAAAA,IAAajB,EAAsB,CACjC+B,QAASnC,EAAO,MAAQA,EAAO,IAAO0B,GAAiBlL,EAAc0B,OAAQ,IAC5E,CACD0J,SAASL,EAAAA,EAAAA,KAAS,IAAMvB,EAAO,MAAQA,EAAO,IAAM,EAClDoC,EAAAA,EAAAA,IAAiB,UAEnBN,EAAG,KAELT,EAAAA,EAAAA,IAAajB,EAAsB,CACjCpC,KAAM,UACNmE,QAAS9D,IACR,CACDuD,SAASL,EAAAA,EAAAA,KAAS,IAAMvB,EAAO,MAAQA,EAAO,IAAM,EAClDoC,EAAAA,EAAAA,IAAiB,UAEnBN,EAAG,SAITF,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAaN,GAAoB,CAC/BsC,QAAS,UACTnN,IAAKQ,EACL4M,MAAOvM,EAAKmB,MACZ4C,MAAOA,EACP,cAAe,SACd,CACD8G,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAaV,EAAyB,CACpCkB,MAAO,OACP/J,KAAM,aACL,CACD8J,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAalB,EAAsB,CACjCsB,WAAYlK,EAAkBW,MAC9B,sBAAuB8H,EAAO,KAAOA,EAAO,GAAM0B,GAAkBnK,EAAmBW,MAAQwJ,GAC/FC,YAAa,QACb4B,SAAU5J,GACT,CACDiI,SAASL,EAAAA,EAAAA,KAAS,IAAM,GACrBJ,EAAAA,EAAAA,KAAW,IAAOC,EAAAA,EAAAA,IAAoBW,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAY5L,EAAS8B,OAAQsL,KAC3ErC,EAAAA,EAAAA,OAAcc,EAAAA,EAAAA,IAAahC,EAAsB,CACvDiC,IAAKsB,EAAKvG,GACV4E,MAAO2B,EAAKtG,KACZhF,MAAOsL,EAAKvG,IACX,KAAM,EAAG,CAAC,QAAS,aACpB,SAEN6E,EAAG,GACF,EAAG,CAAC,kBAETA,EAAG,KAELT,EAAAA,EAAAA,IAAaV,EAAyB,CACpCkB,MAAO,OACP/J,KAAM,cACL,CACD8J,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAalB,EAAsB,CACjCsB,WAAYjK,EAAmBU,MAC/B,sBAAuB8H,EAAO,KAAOA,EAAO,GAAM0B,GAAkBlK,EAAoBU,MAAQwJ,GAChGC,YAAa,QACb8B,UAAWlM,EAAkBW,MAC7BqL,SAAU3J,GACT,CACDgI,SAASL,EAAAA,EAAAA,KAAS,IAAM,GACrBJ,EAAAA,EAAAA,KAAW,IAAOC,EAAAA,EAAAA,IAAoBW,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAYhK,EAAmBE,OAAQsL,KACrFrC,EAAAA,EAAAA,OAAcc,EAAAA,EAAAA,IAAahC,EAAsB,CACvDiC,IAAKsB,EAAKvG,GACV4E,MAAO2B,EAAKtG,KACZhF,MAAOsL,EAAKvG,IACX,KAAM,EAAG,CAAC,QAAS,aACpB,SAEN6E,EAAG,GACF,EAAG,CAAC,aAAc,gBAEvBA,EAAG,KAELT,EAAAA,EAAAA,IAAaV,EAAyB,CACpCkB,MAAO,OACP/J,KAAM,WACL,CACD8J,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAalB,EAAsB,CACjCsB,WAAYhK,EAAgBS,MAC5B,sBAAuB8H,EAAO,KAAOA,EAAO,GAAM0B,GAAkBjK,EAAiBS,MAAQwJ,GAC7FC,YAAa,QACb8B,UAAWjM,EAAmBU,MAC9BqL,SAAU1J,GACT,CACD+H,SAASL,EAAAA,EAAAA,KAAS,IAAM,GACrBJ,EAAAA,EAAAA,KAAW,IAAOC,EAAAA,EAAAA,IAAoBW,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAY1J,EAAeJ,OAAQsL,KACjFrC,EAAAA,EAAAA,OAAcc,EAAAA,EAAAA,IAAahC,EAAsB,CACvDiC,IAAKsB,EAAKvG,GACV4E,MAAO2B,EAAKtG,KACZhF,MAAOsL,EAAKvG,IACX,KAAM,EAAG,CAAC,QAAS,aACpB,SAEN6E,EAAG,GACF,EAAG,CAAC,aAAc,gBAEvBA,EAAG,KAELT,EAAAA,EAAAA,IAAaV,EAAyB,CACpCkB,MAAO,OACP/J,KAAM,aACL,CACD8J,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAalB,EAAsB,CACjCsB,WAAY1K,EAAKmB,MAAMlB,UACvB,sBAAuBgJ,EAAO,KAAOA,EAAO,GAAM0B,GAAkB3K,EAAKmB,MAAMlB,UAAa0K,GAC5FC,YAAa,QACb8B,UAAWhM,EAAgBS,OAC1B,CACD0J,SAASL,EAAAA,EAAAA,KAAS,IAAM,GACrBJ,EAAAA,EAAAA,KAAW,IAAOC,EAAAA,EAAAA,IAAoBW,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAYvJ,EAA0BP,OAAQsL,KAC5FrC,EAAAA,EAAAA,OAAcc,EAAAA,EAAAA,IAAahC,EAAsB,CACvDiC,IAAKsB,EAAKvG,GACV4E,MAAO2B,EAAKtG,KACZhF,MAAOsL,EAAKvG,IACX,KAAM,EAAG,CAAC,QAAS,aACpB,SAEN6E,EAAG,GACF,EAAG,CAAC,aAAc,gBAEvBA,EAAG,KAELT,EAAAA,EAAAA,IAAaV,EAAyB,CACpCkB,MAAO,KACP/J,KAAM,QACL,CACD8J,SAASL,EAAAA,EAAAA,KAAS,IAAM,GACrBJ,EAAAA,EAAAA,KAAW,IAAOC,EAAAA,EAAAA,IAAoBW,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAYrL,EAAYuB,OAAQoC,KAC9E6G,EAAAA,EAAAA,OAAcc,EAAAA,EAAAA,IAAa1B,EAAmB,CACpD2B,IAAK5H,EACLoJ,SAAU,GACV,uBAAuB,EACvBP,QAAUzB,GAAiBrH,EAAeC,IACzC,CACDsH,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBa,EAAAA,EAAAA,KAAiBO,EAAAA,EAAAA,IAAiBrI,GAAM,MAE1CwH,EAAG,GACF,KAAM,CAAC,eACR,MACHlL,EAAgBsB,QACZiJ,EAAAA,EAAAA,OAAcc,EAAAA,EAAAA,IAAarB,EAAqB,CAC/CsB,IAAK,EACLmB,QAAS,cACTnN,IAAKY,EACL2K,WAAY5K,EAAcqB,MAC1B,sBAAuB8H,EAAO,KAAOA,EAAO,GAAM0B,GAAkB7K,EAAeqB,MAAQwJ,GAC3FnM,MAAO,YACPgK,KAAM,QACNoE,SAASC,EAAAA,EAAAA,IAAU3J,EAAuB,CAAC,UAC3C4J,OAAQ5J,GACP,KAAM,EAAG,CAAC,kBACZkH,EAAAA,EAAAA,OAAcc,EAAAA,EAAAA,IAAa7B,EAAsB,CAChD8B,IAAK,EACL3M,MAAO,iBACPgK,KAAM,QACN4C,QAASrI,GACR,CACD8H,SAASL,EAAAA,EAAAA,KAAS,IAAMvB,EAAO,MAAQA,EAAO,IAAM,EAClDoC,EAAAA,EAAAA,IAAiB,gBAEnBN,EAAG,QAGXA,EAAG,KAELT,EAAAA,EAAAA,IAAaV,EAAyB,CACpCkB,MAAO,KACP/J,KAAM,YACL,CACD8J,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAaR,GAA4B,CACvCY,WAAY1K,EAAKmB,MAAMf,SACvB,sBAAuB6I,EAAO,KAAOA,EAAO,GAAM0B,GAAkB3K,EAAKmB,MAAMf,SAAYuK,GAC3FoC,IAAK,EACLC,UAAW,GACV,KAAM,EAAG,CAAC,kBAEfjC,EAAG,KAELT,EAAAA,EAAAA,IAAaV,EAAyB,CACpCkB,MAAO,MACP/J,KAAM,SACL,CACD8J,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAaR,GAA4B,CACvCY,WAAY1K,EAAKmB,MAAMhB,MACvB,sBAAuB8I,EAAO,KAAOA,EAAO,GAAM0B,GAAkB3K,EAAKmB,MAAMhB,MAASwK,GACxFqC,UAAW,EACXC,KAAM,GACNF,IAAK,GACJ,KAAM,EAAG,CAAC,kBAEfhC,EAAG,KAELT,EAAAA,EAAAA,IAAaV,EAAyB,CACpCkB,MAAO,OACP/J,KAAM,aACL,CACD8J,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAaR,GAA4B,CACvCY,WAAY1K,EAAKmB,MAAMb,UACvB,sBAAuB2I,EAAO,KAAOA,EAAO,GAAM0B,GAAkB3K,EAAKmB,MAAMb,UAAaqK,GAC5FoC,IAAK,EACLC,UAAW,EACXC,KAAM,EACNrC,YAAa,WACZ,KAAM,EAAG,CAAC,kBAEfG,EAAG,KAELT,EAAAA,EAAAA,IAAaV,EAAyB,CACpCkB,MAAO,OACP/J,KAAM,UACL,CACD8J,SAASL,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAaP,GAA2B,CACtCW,WAAY1K,EAAKmB,MAAMd,OACvB,sBAAuB4I,EAAO,MAAQA,EAAO,IAAO0B,GAAkB3K,EAAKmB,MAAMd,OAAUsK,GAC3F1D,KAAM,WACN2D,YAAa,OACbrE,OAAQ,sBACR,eAAgB,uBACf,KAAM,EAAG,CAAC,kBAEfwE,EAAG,OAGPA,EAAG,GACF,EAAG,CAAC,aAETA,EAAG,GACF,EAAG,CAAC,QAAS,gBAChB,CAEJ,I,UEp7BA,MAAMmC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O,+2IC2DO,MAAMnI,EAAcA,KAClBoI,EAAAA,EAAAA,GAAQ,CACbC,IAAK,iBACLC,OAAQ,QAICrI,EAAgBA,KACpBmI,EAAAA,EAAAA,GAAQ,CACbC,IAAK,mBACLC,OAAQ,QAICpI,EAAYA,KAChBkI,EAAAA,EAAAA,GAAQ,CACbC,IAAK,eACLC,OAAQ,QAICvI,EAAcA,KAClBqI,EAAAA,EAAAA,GAAQ,CACbC,IAAK,iBACLC,OAAQ,QAICxI,EAAgBA,KACpBsI,EAAAA,EAAAA,GAAQ,CACbC,IAAK,mBACLC,OAAQ,QAIC3F,EAAmB4F,IACvBH,EAAAA,EAAAA,GAAQ,CACbC,IAAK,mBACLC,OAAQ,OACRlI,KAAMmI,IAIG7F,EAAmB8F,IACvBJ,EAAAA,EAAAA,GAAQ,CACbC,IAAK,mBACLC,OAAQ,OACRlI,KAAMoI,IAIGpG,EAAmBqG,IACvBL,EAAAA,EAAAA,GAAQ,CACbC,IAAK,mBACLC,OAAQ,OACR/E,OAAQ,CAAEkF,iBAYD/E,EAAkBA,CAACxI,EAAmB+H,KACjD,MAAMyF,EAAW,IAAIC,SAGrB,OAFAD,EAASE,OAAO,YAAa1N,EAAU2N,YACvCH,EAASE,OAAO,QAAS3F,IAClBmF,EAAAA,EAAAA,GAAQ,CACbC,IAAK,mBACLC,OAAQ,OACRlI,KAAMsI,EACNI,QAAS,CACP,eAAgB,wBAElB,EAGS3I,EAAmBA,KACvBiI,EAAAA,EAAAA,GAAQ,CACbC,IAAK,sBACLC,OAAQ,O", "sources": ["webpack://admin-web/./src/views/product/ProductView.vue?8326", "webpack://admin-web/./src/views/product/ProductView.vue", "webpack://admin-web/./src/views/product/ProductView.vue?8e8c", "webpack://admin-web/./src/api/product.ts"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, unref as _unref, resolveDirective as _resolveDirective, withDirectives as _withDirectives, withKeys as _withKeys, createCommentVNode as _createCommentVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"product-view\" }\nconst _hoisted_2 = { class: \"card-header\" }\nconst _hoisted_3 = { class: \"header-operations\" }\nconst _hoisted_4 = { class: \"filter-group\" }\nconst _hoisted_5 = { class: \"tags-container\" }\nconst _hoisted_6 = { class: \"dialog-footer\" }\n\nimport { ref, computed, onMounted, nextTick } from 'vue'\n  import { ElMessage, ElMessageBox } from 'element-plus'\n  import type { FormInstance } from 'element-plus'\n  import { \n    getMPProducts, \n    getProducts,\n    getServices,\n    getCategories,\n    getBrands,\n    createMPProduct, \n    modifyMPProduct, \n    deleteMPProduct,\n    setProductImage,\n    getProductImages\n  } from '@/api/product'\n  import type { \n    MPProduct,\n    ProductImage,\n    Product,\n    Service,\n    Category,\n    Brand,\n    MPProductCreateParams, \n    MPProductUpdateParams \n  } from '@/api/product'\n  import dayjs from 'dayjs'\n\n  \nexport default /*@__PURE__*/_defineComponent({\n  __name: 'ProductView',\n  setup(__props) {\n\n  const activityProducts = ref<MPProduct[]>([])\n  const inventoryProducts = ref<Product[]>([])\n  const services = ref<Service[]>([])\n  const categories = ref<Category[]>([])\n  const brands = ref<Brand[]>([])\n  const loading = ref(false)\n  const dialogVisible = ref(false)\n  const dialogTitle = ref('')\n  const formRef = ref<FormInstance>()\n\n  // 标签相关\n  const dynamicTags = ref<string[]>([])\n  const inputTagVisible = ref(false)\n  const inputTagValue = ref('')\n  const tagInputRef = ref<HTMLInputElement>()\n\n  const form = ref<Partial<MPProductUpdateParams>>({\n    productId: undefined,\n    price: 0,\n    quantity: 0,\n    expiry: '',\n    validDays: 7,\n    tags: ''\n  })\n\n  const selectedServiceId = ref<number>()\n  const selectedCategoryId = ref<number>()\n  const selectedBrandId = ref<number>()\n\n  // 筛选变量\n  const filterStatus = ref('')  // 空字符串对应\"全部\"\n  const filterServiceId = ref('')  // 空字符串对应\"全部\"\n\n  // 添加商品图片数据\n  const productImages = ref<ProductImage[]>([])\n\n  // 添加排序配置\n  const sortConfig = ref({\n    prop: '',\n    order: ''\n  })\n\n  // 过滤计算属性\n  const filteredCategories = computed(() => {\n    if (!selectedServiceId.value) return []\n    return categories.value.filter(c => c.serviceId === selectedServiceId.value)\n  })\n\n  const filteredBrands = computed(() => {\n    if (!selectedCategoryId.value) return []\n    return brands.value.filter(b => b.categoryId === selectedCategoryId.value)\n  })\n\n  const filteredInventoryProducts = computed(() => {\n    if (!selectedBrandId.value) return []\n    return inventoryProducts.value.filter(p => p.brandId === selectedBrandId.value)\n  })\n\n  // 修改筛选逻辑，加入排序\n  const filteredProducts = computed(() => {\n    let result = [...activityProducts.value]\n    \n    // 按状态筛选\n    if (filterStatus.value !== '') {\n      result = result.filter(product => {\n        const isActive = dayjs(product.expiry).isAfter(dayjs())\n        if (filterStatus.value === 'active') {\n          return isActive\n        } else if (filterStatus.value === 'inactive') {\n          return !isActive\n        }\n        return true\n      })\n    }\n    \n    // 按服务类型名称筛选\n    if (filterServiceId.value !== '') {\n      result = result.filter(product => {\n        return getServiceName(product.productId) === filterServiceId.value\n      })\n    }\n    \n    // 应用排序\n    if (sortConfig.value.prop && sortConfig.value.order) {\n      const prop = sortConfig.value.prop\n      const isAsc = sortConfig.value.order === 'ascending'\n      \n      result.sort((a, b) => {\n        let comparison = 0\n        if (prop === 'quantity') {\n          comparison = a.quantity - b.quantity\n        } else if (prop === 'price') {\n          const priceA = typeof a.price === 'string' ? parseFloat(a.price) : a.price\n          const priceB = typeof b.price === 'string' ? parseFloat(b.price) : b.price\n          comparison = priceA - priceB\n        } else if (prop === 'validDays') {\n          comparison = a.validDays - b.validDays\n        } else if (prop === 'expiry') {\n          comparison = dayjs(a.expiry).isBefore(dayjs(b.expiry)) ? -1 : 1\n        }\n        \n        return isAsc ? comparison : -comparison\n      })\n    }\n    \n    return result\n  })\n\n  // 处理选择变化\n  const handleServiceChange = () => {\n    selectedCategoryId.value = undefined\n    selectedBrandId.value = undefined\n    form.value.productId = undefined\n  }\n\n  const handleCategoryChange = () => {\n    selectedBrandId.value = undefined\n    form.value.productId = undefined\n  }\n\n  const handleBrandChange = () => {\n    form.value.productId = undefined\n  }\n\n  // 标签相关方法\n  const showTagInput = () => {\n    inputTagVisible.value = true\n    nextTick(() => {\n      tagInputRef.value?.focus()\n    })\n  }\n\n  const handleTagInputConfirm = () => {\n    if (inputTagValue.value) {\n      if (!dynamicTags.value.includes(inputTagValue.value)) {\n        dynamicTags.value.push(inputTagValue.value)\n        updateFormTags()\n      }\n    }\n    inputTagVisible.value = false\n    inputTagValue.value = ''\n  }\n\n  const handleTagClose = (tag: string) => {\n    dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1)\n    updateFormTags()\n  }\n\n  const updateFormTags = () => {\n    form.value.tags = dynamicTags.value.join(' ')\n  }\n\n  const getTags = (tagsString: string): string[] => {\n    if (!tagsString) return []\n    return tagsString.split(' ').filter(tag => tag.trim() !== '')\n  }\n\n  const rules = {\n    productId: [\n      { required: true, message: '请选择产品', trigger: 'change' }\n    ],\n    price: [\n      { required: true, message: '请输入价格', trigger: 'blur' }\n    ],\n    quantity: [\n      { required: true, message: '请输入库存', trigger: 'blur' }\n    ],\n    expiry: [\n      { required: true, message: '请选择有效期', trigger: 'change' }\n    ],\n    validDays: [\n      { required: true, message: '请输入有效天数', trigger: 'blur' }\n    ]\n  }\n\n  const fetchAllData = async () => {\n    loading.value = true\n    try {\n      const [activityProductsRes, inventoryProductsRes, servicesRes, categoriesRes, brandsRes, imagesRes] = await Promise.all([\n        getMPProducts(),\n        getProducts(),\n        getServices(),\n        getCategories(),\n        getBrands(),\n        getProductImages()\n      ])\n      activityProducts.value = activityProductsRes.data\n      inventoryProducts.value = inventoryProductsRes.data\n      services.value = servicesRes.data\n      categories.value = categoriesRes.data\n      brands.value = brandsRes.data\n      productImages.value = imagesRes.data.map((img: ProductImage) => ({\n        ...img,\n        productId: parseInt(String(img.productId))\n      }))\n    } catch (error) {\n      ElMessage.error('获取数据失败')\n      console.error('获取数据失败:', error)\n    } finally {\n      loading.value = false\n    }\n  }\n\n  onMounted(fetchAllData)\n\n  const formatPrice = (price: number) => {\n    // 确保 price 是数字\n    const numPrice = typeof price === 'string' ? parseFloat(price) : price\n    \n    // 检查是否为有效数字\n    if (isNaN(numPrice)) {\n      return '¥0.00'\n    }\n    \n    return `¥ ${numPrice.toFixed(2)}`\n  }\n\n  const getProductName = (productId: number) => {\n    const product = inventoryProducts.value.find(p => p.id == productId)\n    return product?.name || '-'\n  }\n\n  const getServiceName = (productId: number) => {\n    const product = inventoryProducts.value.find(p => p.id == productId)\n    const service = services.value.find(s => s.id == product?.serviceId)\n    return service?.name || '-'\n  }\n\n  const handleAdd = () => {\n    dialogTitle.value = '新增商品'\n    form.value = {\n      productId: undefined,\n      price: 1,\n      quantity: 1,\n      expiry: dayjs().format('YYYY-MM-DDTHH:mm:ss'),\n      validDays: 7,\n      tags: ''\n    }\n    dynamicTags.value = []\n    selectedServiceId.value = undefined\n    selectedCategoryId.value = undefined\n    selectedBrandId.value = undefined\n    dialogVisible.value = true\n  }\n\n  const handleEdit = (row: MPProduct) => {\n    dialogTitle.value = '编辑产品'\n    const product = inventoryProducts.value.find(p => p.id == row.productId)\n    if (product) {\n      selectedBrandId.value = product.brandId\n      const brand = brands.value.find(b => b.id == product.brandId)\n      if (brand) {\n        selectedCategoryId.value = brand.categoryId\n        const category = categories.value.find(c => c.id == brand.categoryId)\n        if (category) {\n          selectedServiceId.value = category.serviceId\n        }\n      }\n    }\n    \n    form.value = {\n      id: row.id,\n      productId: row.productId,\n      price: row.price,\n      quantity: row.quantity,\n      expiry: row.expiry,\n      validDays: row.validDays,\n      tags: row.tags || ''\n    }\n    \n    // 设置标签数组\n    dynamicTags.value = getTags(row.tags || '')\n    \n    dialogVisible.value = true\n  }\n\n  const handleDelete = (row: MPProduct) => {\n    ElMessageBox.confirm(\n      '确定要删除这个产品吗？',\n      '警告',\n      {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning',\n      }\n    ).then(async () => {\n      try {\n        await deleteMPProduct(row.id)\n        await fetchAllData()\n        ElMessage.success('删除成功')\n      } catch (error) {\n        ElMessage.error('删除失败')\n      }\n    }).catch(() => {\n      // 点击取消按钮\n    })\n  }\n\n  const handleSubmit = async () => {\n    if (!formRef.value) return\n    \n    await formRef.value.validate(async (valid) => {\n      if (valid) {\n        try {\n          if (form.value.id) {\n            await modifyMPProduct(form.value as MPProductUpdateParams)\n          } else {\n            await createMPProduct(form.value as MPProductCreateParams)\n          }\n          dialogVisible.value = false\n          await fetchAllData()\n          ElMessage.success(form.value.id ? '编辑成功' : '新增成功')\n        } catch (error) {\n          ElMessage.error(form.value.id ? '编辑失败' : '新增失败')\n        }\n      }\n    })\n  }\n\n  const handleDialogClose = () => {\n    if (formRef.value) {\n      formRef.value.resetFields()\n    }\n    selectedServiceId.value = undefined\n    selectedCategoryId.value = undefined\n    selectedBrandId.value = undefined\n    dynamicTags.value = []\n    inputTagVisible.value = false\n    inputTagValue.value = ''\n  }\n\n  // 添加状态判断方法\n  const getStatusType = (expiry: string) => {\n    return dayjs(expiry).isAfter(dayjs()) ? 'success' : 'danger'\n  }\n\n  const getStatusText = (expiry: string) => {\n    return dayjs(expiry).isAfter(dayjs()) ? '在售' : '下架'\n  }\n\n  // 修改获取商品图片的方法\n  const getProductImage = (productId: number) => {\n    const image = productImages.value.find(img => img.productId == productId)\n    if (!image)\n      return new URL('@/assets/default.png', import.meta.url).href // 使用URL构造函数和import.meta.url来正确引用assets目录下的静态资源\n    return process.env.VUE_APP_API_BASE_URL + \"/productCover/\" + image?.fileName\n  }\n\n  // 添加上传处理方法\n  const handleUpload = async (params: { file: File }, row: MPProduct) => {\n    try {\n      // 检查文件大小是否超过5MB\n      if (params.file.size > 5 * 1024 * 1024) {\n        ElMessage.error('图片大小不能超过5MB')\n        return\n      }\n\n      await setProductImage(row.productId, params.file)\n      ElMessage.success('上传成功')\n      await fetchAllData()  // 重新加载数据以显示新图片\n    } catch (error) {\n      ElMessage.error('上传失败')\n      console.error('上传失败:', error)\n    }\n  }\n\n  // 为产品表格的相应列添加排序功能\n  const sortByQuantity = (a: MPProduct, b: MPProduct) => {\n    return a.quantity - b.quantity\n  }\n\n  const sortByPrice = (a: MPProduct, b: MPProduct) => {\n    // 确保价格是数字\n    const priceA = typeof a.price === 'string' ? parseFloat(a.price) : a.price\n    const priceB = typeof b.price === 'string' ? parseFloat(b.price) : b.price\n    return priceA - priceB\n  }\n\n  const sortByValidDays = (a: MPProduct, b: MPProduct) => {\n    return a.validDays - b.validDays\n  }\n\n  const sortByEndDate = (a: MPProduct, b: MPProduct) => {\n    return dayjs(a.expiry).isBefore(dayjs(b.expiry)) ? -1 : 1\n  }\n\n  // 处理排序\n  const handleSort = (column: { prop: string; order: string }) => {\n    sortConfig.value.prop = column.prop\n    sortConfig.value.order = column.order\n  }\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_option = _resolveComponent(\"el-option\")!\n  const _component_el_select = _resolveComponent(\"el-select\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_el_image = _resolveComponent(\"el-image\")!\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n  const _component_el_upload = _resolveComponent(\"el-upload\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n  const _component_el_card = _resolveComponent(\"el-card\")!\n  const _component_el_form_item = _resolveComponent(\"el-form-item\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_input_number = _resolveComponent(\"el-input-number\")!\n  const _component_el_date_picker = _resolveComponent(\"el-date-picker\")!\n  const _component_el_form = _resolveComponent(\"el-form\")!\n  const _component_el_dialog = _resolveComponent(\"el-dialog\")!\n  const _directive_loading = _resolveDirective(\"loading\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createVNode(_component_el_card, { class: \"box-card\" }, {\n      header: _withCtx(() => [\n        _createElementVNode(\"div\", _hoisted_2, [\n          _cache[14] || (_cache[14] = _createElementVNode(\"span\", null, \"商品管理\", -1)),\n          _createElementVNode(\"div\", _hoisted_3, [\n            _createElementVNode(\"div\", _hoisted_4, [\n              _createVNode(_component_el_select, {\n                modelValue: filterStatus.value,\n                \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((filterStatus).value = $event)),\n                placeholder: \"全部\",\n                class: \"filter-item status-filter\"\n              }, {\n                default: _withCtx(() => [\n                  _createVNode(_component_el_option, {\n                    label: \"全部\",\n                    value: \"\"\n                  }),\n                  _createVNode(_component_el_option, {\n                    label: \"在售\",\n                    value: \"active\"\n                  }),\n                  _createVNode(_component_el_option, {\n                    label: \"下架\",\n                    value: \"inactive\"\n                  })\n                ]),\n                _: 1\n              }, 8, [\"modelValue\"]),\n              _createVNode(_component_el_select, {\n                modelValue: filterServiceId.value,\n                \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((filterServiceId).value = $event)),\n                placeholder: \"全部\",\n                class: \"filter-item service-filter\"\n              }, {\n                default: _withCtx(() => [\n                  _createVNode(_component_el_option, {\n                    label: \"全部\",\n                    value: \"\"\n                  }),\n                  (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(services.value, (service) => {\n                    return (_openBlock(), _createBlock(_component_el_option, {\n                      key: service.id,\n                      label: service.name,\n                      value: service.name\n                    }, null, 8, [\"label\", \"value\"]))\n                  }), 128))\n                ]),\n                _: 1\n              }, 8, [\"modelValue\"])\n            ]),\n            _createVNode(_component_el_button, {\n              type: \"primary\",\n              onClick: handleAdd\n            }, {\n              default: _withCtx(() => _cache[13] || (_cache[13] = [\n                _createTextVNode(\"新增商品\")\n              ])),\n              _: 1\n            })\n          ])\n        ])\n      ]),\n      default: _withCtx(() => [\n        _withDirectives((_openBlock(), _createBlock(_component_el_table, {\n          data: filteredProducts.value,\n          style: {\"width\":\"100%\"},\n          onSortChange: handleSort\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_component_el_table_column, {\n              prop: \"id\",\n              label: \"商品号\",\n              width: \"80\"\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"图片\",\n              width: \"80\"\n            }, {\n              default: _withCtx(({ row }) => [\n                _createVNode(_component_el_image, {\n                  src: getProductImage(row.productId),\n                  \"preview-src-list\": [getProductImage(row.productId)],\n                  fit: \"cover\",\n                  style: {\"width\":\"30px\",\"height\":\"30px\"},\n                  \"preview-teleported\": \"\"\n                }, null, 8, [\"src\", \"preview-src-list\"])\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"状态\",\n              width: \"100\"\n            }, {\n              default: _withCtx(({ row }) => [\n                _createVNode(_component_el_tag, {\n                  type: getStatusType(row.expiry)\n                }, {\n                  default: _withCtx(() => [\n                    _createTextVNode(_toDisplayString(getStatusText(row.expiry)), 1)\n                  ]),\n                  _: 2\n                }, 1032, [\"type\"])\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"服务类型\",\n              width: \"150\"\n            }, {\n              default: _withCtx(({ row }) => [\n                _createTextVNode(_toDisplayString(getServiceName(row.productId)), 1)\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              prop: \"productId\",\n              label: \"产品号\",\n              width: \"100\"\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"产品名称\",\n              \"min-width\": \"200\"\n            }, {\n              default: _withCtx(({ row }) => [\n                _createTextVNode(_toDisplayString(getProductName(row.productId)), 1)\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"标签\",\n              width: \"150\"\n            }, {\n              default: _withCtx(({ row }) => [\n                _createElementVNode(\"div\", _hoisted_5, [\n                  (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(getTags(row.tags), (tag, index) => {\n                    return (_openBlock(), _createBlock(_component_el_tag, {\n                      key: index,\n                      size: \"small\",\n                      class: \"tag-item\"\n                    }, {\n                      default: _withCtx(() => [\n                        _createTextVNode(_toDisplayString(tag), 1)\n                      ]),\n                      _: 2\n                    }, 1024))\n                  }), 128))\n                ])\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"数量\",\n              width: \"120\",\n              sortable: \"\",\n              \"sort-method\": sortByQuantity\n            }, {\n              default: _withCtx(({ row }) => [\n                _createTextVNode(_toDisplayString(row.quantity), 1)\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"总售价\",\n              width: \"120\",\n              sortable: \"\",\n              \"sort-method\": sortByPrice\n            }, {\n              default: _withCtx(({ row }) => [\n                _createTextVNode(_toDisplayString(formatPrice(row.price)), 1)\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"有效天数\",\n              width: \"120\",\n              sortable: \"\",\n              \"sort-method\": sortByValidDays\n            }, {\n              default: _withCtx(({ row }) => [\n                _createTextVNode(_toDisplayString(row.validDays), 1)\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"截止日期\",\n              width: \"180\",\n              sortable: \"\",\n              \"sort-method\": sortByEndDate\n            }, {\n              default: _withCtx(({ row }) => [\n                _createTextVNode(_toDisplayString(_unref(dayjs)(row.expiry).format('YYYY-MM-DD HH:mm:ss')), 1)\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"操作\",\n              width: \"250\",\n              fixed: \"right\"\n            }, {\n              default: _withCtx((scope) => [\n                _createVNode(_component_el_button, {\n                  size: \"small\",\n                  onClick: ($event: any) => (handleEdit(scope.row))\n                }, {\n                  default: _withCtx(() => _cache[15] || (_cache[15] = [\n                    _createTextVNode(\"编辑\")\n                  ])),\n                  _: 2\n                }, 1032, [\"onClick\"]),\n                _createVNode(_component_el_upload, {\n                  class: \"upload-button\",\n                  \"show-file-list\": false,\n                  \"http-request\": (params) => handleUpload(params, scope.row),\n                  accept: \"image/*\"\n                }, {\n                  default: _withCtx(() => [\n                    _createVNode(_component_el_button, {\n                      size: \"small\",\n                      type: \"primary\"\n                    }, {\n                      default: _withCtx(() => _cache[16] || (_cache[16] = [\n                        _createTextVNode(\"上传图片\")\n                      ])),\n                      _: 1\n                    })\n                  ]),\n                  _: 2\n                }, 1032, [\"http-request\"]),\n                _createVNode(_component_el_button, {\n                  size: \"small\",\n                  type: \"danger\",\n                  onClick: ($event: any) => (handleDelete(scope.row))\n                }, {\n                  default: _withCtx(() => _cache[17] || (_cache[17] = [\n                    _createTextVNode(\"删除\")\n                  ])),\n                  _: 2\n                }, 1032, [\"onClick\"])\n              ]),\n              _: 1\n            })\n          ]),\n          _: 1\n        }, 8, [\"data\"])), [\n          [_directive_loading, loading.value]\n        ])\n      ]),\n      _: 1\n    }),\n    _createVNode(_component_el_dialog, {\n      title: dialogTitle.value,\n      modelValue: dialogVisible.value,\n      \"onUpdate:modelValue\": _cache[12] || (_cache[12] = ($event: any) => ((dialogVisible).value = $event)),\n      width: \"500px\",\n      onClose: handleDialogClose\n    }, {\n      footer: _withCtx(() => [\n        _createElementVNode(\"span\", _hoisted_6, [\n          _createVNode(_component_el_button, {\n            onClick: _cache[11] || (_cache[11] = ($event: any) => (dialogVisible.value = false))\n          }, {\n            default: _withCtx(() => _cache[19] || (_cache[19] = [\n              _createTextVNode(\"取消\")\n            ])),\n            _: 1\n          }),\n          _createVNode(_component_el_button, {\n            type: \"primary\",\n            onClick: handleSubmit\n          }, {\n            default: _withCtx(() => _cache[20] || (_cache[20] = [\n              _createTextVNode(\"确定\")\n            ])),\n            _: 1\n          })\n        ])\n      ]),\n      default: _withCtx(() => [\n        _createVNode(_component_el_form, {\n          ref_key: \"formRef\",\n          ref: formRef,\n          model: form.value,\n          rules: rules,\n          \"label-width\": \"100px\"\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_component_el_form_item, {\n              label: \"选择服务\",\n              prop: \"serviceId\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_select, {\n                  modelValue: selectedServiceId.value,\n                  \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((selectedServiceId).value = $event)),\n                  placeholder: \"请选择服务\",\n                  onChange: handleServiceChange\n                }, {\n                  default: _withCtx(() => [\n                    (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(services.value, (item) => {\n                      return (_openBlock(), _createBlock(_component_el_option, {\n                        key: item.id,\n                        label: item.name,\n                        value: item.id\n                      }, null, 8, [\"label\", \"value\"]))\n                    }), 128))\n                  ]),\n                  _: 1\n                }, 8, [\"modelValue\"])\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_form_item, {\n              label: \"选择类别\",\n              prop: \"categoryId\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_select, {\n                  modelValue: selectedCategoryId.value,\n                  \"onUpdate:modelValue\": _cache[3] || (_cache[3] = ($event: any) => ((selectedCategoryId).value = $event)),\n                  placeholder: \"请选择类别\",\n                  disabled: !selectedServiceId.value,\n                  onChange: handleCategoryChange\n                }, {\n                  default: _withCtx(() => [\n                    (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(filteredCategories.value, (item) => {\n                      return (_openBlock(), _createBlock(_component_el_option, {\n                        key: item.id,\n                        label: item.name,\n                        value: item.id\n                      }, null, 8, [\"label\", \"value\"]))\n                    }), 128))\n                  ]),\n                  _: 1\n                }, 8, [\"modelValue\", \"disabled\"])\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_form_item, {\n              label: \"选择品牌\",\n              prop: \"brandId\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_select, {\n                  modelValue: selectedBrandId.value,\n                  \"onUpdate:modelValue\": _cache[4] || (_cache[4] = ($event: any) => ((selectedBrandId).value = $event)),\n                  placeholder: \"请选择品牌\",\n                  disabled: !selectedCategoryId.value,\n                  onChange: handleBrandChange\n                }, {\n                  default: _withCtx(() => [\n                    (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(filteredBrands.value, (item) => {\n                      return (_openBlock(), _createBlock(_component_el_option, {\n                        key: item.id,\n                        label: item.name,\n                        value: item.id\n                      }, null, 8, [\"label\", \"value\"]))\n                    }), 128))\n                  ]),\n                  _: 1\n                }, 8, [\"modelValue\", \"disabled\"])\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_form_item, {\n              label: \"选择产品\",\n              prop: \"productId\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_select, {\n                  modelValue: form.value.productId,\n                  \"onUpdate:modelValue\": _cache[5] || (_cache[5] = ($event: any) => ((form.value.productId) = $event)),\n                  placeholder: \"请选择产品\",\n                  disabled: !selectedBrandId.value\n                }, {\n                  default: _withCtx(() => [\n                    (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(filteredInventoryProducts.value, (item) => {\n                      return (_openBlock(), _createBlock(_component_el_option, {\n                        key: item.id,\n                        label: item.name,\n                        value: item.id\n                      }, null, 8, [\"label\", \"value\"]))\n                    }), 128))\n                  ]),\n                  _: 1\n                }, 8, [\"modelValue\", \"disabled\"])\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_form_item, {\n              label: \"标签\",\n              prop: \"tags\"\n            }, {\n              default: _withCtx(() => [\n                (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(dynamicTags.value, (tag) => {\n                  return (_openBlock(), _createBlock(_component_el_tag, {\n                    key: tag,\n                    closable: \"\",\n                    \"disable-transitions\": false,\n                    onClose: ($event: any) => (handleTagClose(tag))\n                  }, {\n                    default: _withCtx(() => [\n                      _createTextVNode(_toDisplayString(tag), 1)\n                    ]),\n                    _: 2\n                  }, 1032, [\"onClose\"]))\n                }), 128)),\n                (inputTagVisible.value)\n                  ? (_openBlock(), _createBlock(_component_el_input, {\n                      key: 0,\n                      ref_key: \"tagInputRef\",\n                      ref: tagInputRef,\n                      modelValue: inputTagValue.value,\n                      \"onUpdate:modelValue\": _cache[6] || (_cache[6] = ($event: any) => ((inputTagValue).value = $event)),\n                      class: \"tag-input\",\n                      size: \"small\",\n                      onKeyup: _withKeys(handleTagInputConfirm, [\"enter\"]),\n                      onBlur: handleTagInputConfirm\n                    }, null, 8, [\"modelValue\"]))\n                  : (_openBlock(), _createBlock(_component_el_button, {\n                      key: 1,\n                      class: \"button-new-tag\",\n                      size: \"small\",\n                      onClick: showTagInput\n                    }, {\n                      default: _withCtx(() => _cache[18] || (_cache[18] = [\n                        _createTextVNode(\" + 添加标签 \")\n                      ])),\n                      _: 1\n                    }))\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_form_item, {\n              label: \"数量\",\n              prop: \"quantity\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_input_number, {\n                  modelValue: form.value.quantity,\n                  \"onUpdate:modelValue\": _cache[7] || (_cache[7] = ($event: any) => ((form.value.quantity) = $event)),\n                  min: 0,\n                  precision: 0\n                }, null, 8, [\"modelValue\"])\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_form_item, {\n              label: \"总价格\",\n              prop: \"price\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_input_number, {\n                  modelValue: form.value.price,\n                  \"onUpdate:modelValue\": _cache[8] || (_cache[8] = ($event: any) => ((form.value.price) = $event)),\n                  precision: 2,\n                  step: 0.1,\n                  min: 0\n                }, null, 8, [\"modelValue\"])\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_form_item, {\n              label: \"有效天数\",\n              prop: \"validDays\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_input_number, {\n                  modelValue: form.value.validDays,\n                  \"onUpdate:modelValue\": _cache[9] || (_cache[9] = ($event: any) => ((form.value.validDays) = $event)),\n                  min: 1,\n                  precision: 0,\n                  step: 1,\n                  placeholder: \"请输入有效天数\"\n                }, null, 8, [\"modelValue\"])\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_form_item, {\n              label: \"截止时间\",\n              prop: \"expiry\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_date_picker, {\n                  modelValue: form.value.expiry,\n                  \"onUpdate:modelValue\": _cache[10] || (_cache[10] = ($event: any) => ((form.value.expiry) = $event)),\n                  type: \"datetime\",\n                  placeholder: \"选择时间\",\n                  format: \"YYYY-MM-DD HH:mm:ss\",\n                  \"value-format\": \"YYYY-MM-DDTHH:mm:ss\"\n                }, null, 8, [\"modelValue\"])\n              ]),\n              _: 1\n            })\n          ]),\n          _: 1\n        }, 8, [\"model\"])\n      ]),\n      _: 1\n    }, 8, [\"title\", \"modelValue\"])\n  ]))\n}\n}\n\n})", "<template>\n  <div class=\"product-view\">\n    <el-card class=\"box-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <span>商品管理</span>\n          <div class=\"header-operations\">\n            <div class=\"filter-group\">\n              <el-select\n                v-model=\"filterStatus\"\n                placeholder=\"全部\"\n                class=\"filter-item status-filter\"\n              >\n                <el-option label=\"全部\" value=\"\" />\n                <el-option label=\"在售\" value=\"active\" />\n                <el-option label=\"下架\" value=\"inactive\" />\n              </el-select>\n\n              <el-select\n                v-model=\"filterServiceId\"\n                placeholder=\"全部\"\n                class=\"filter-item service-filter\"\n              >\n                <el-option label=\"全部\" value=\"\" />\n                <el-option\n                  v-for=\"service in services\"\n                  :key=\"service.id\"\n                  :label=\"service.name\"\n                  :value=\"service.name\"\n                />\n              </el-select>\n            </div>\n\n            <el-button type=\"primary\" @click=\"handleAdd\">新增商品</el-button>\n          </div>\n        </div>\n      </template>\n      \n      <el-table v-loading=\"loading\" :data=\"filteredProducts\" style=\"width: 100%\" @sort-change=\"handleSort\">\n        <el-table-column prop=\"id\" label=\"商品号\" width=\"80\" />\n        <el-table-column label=\"图片\" width=\"80\">\n          <template #default=\"{ row }\">\n            <el-image\n              :src=\"getProductImage(row.productId)\"\n              :preview-src-list=\"[getProductImage(row.productId)]\"\n              fit=\"cover\"\n              style=\"width: 30px; height: 30px;\"\n              preview-teleported\n            />\n          </template>\n        </el-table-column>\n        <el-table-column label=\"状态\" width=\"100\">\n          <template #default=\"{ row }\">\n            <el-tag :type=\"getStatusType(row.expiry)\">\n              {{ getStatusText(row.expiry) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"服务类型\" width=\"150\">\n          <template #default=\"{ row }\">\n            {{ getServiceName(row.productId) }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"productId\" label=\"产品号\" width=\"100\" />\n        <el-table-column label=\"产品名称\" min-width=\"200\">\n          <template #default=\"{ row }\">\n            {{ getProductName(row.productId) }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"标签\" width=\"150\">\n          <template #default=\"{ row }\">\n            <div class=\"tags-container\">\n              <el-tag\n                v-for=\"(tag, index) in getTags(row.tags)\"\n                :key=\"index\"\n                size=\"small\"\n                class=\"tag-item\"\n              >\n                {{ tag }}\n              </el-tag>\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column \n          label=\"数量\" \n          width=\"120\" \n          sortable \n          :sort-method=\"sortByQuantity\"\n        >\n          <template #default=\"{ row }\">\n            {{ row.quantity }}\n          </template>\n        </el-table-column>\n        <el-table-column \n          label=\"总售价\" \n          width=\"120\" \n          sortable \n          :sort-method=\"sortByPrice\"\n        >\n          <template #default=\"{ row }\">\n            {{ formatPrice(row.price) }}\n          </template>\n        </el-table-column>\n        <el-table-column \n          label=\"有效天数\" \n          width=\"120\" \n          sortable \n          :sort-method=\"sortByValidDays\"\n        >\n          <template #default=\"{ row }\">\n            {{ row.validDays }}\n          </template>\n        </el-table-column>\n        <el-table-column \n          label=\"截止日期\" \n          width=\"180\" \n          sortable \n          :sort-method=\"sortByEndDate\"\n        >\n          <template #default=\"{ row }\">\n            {{ dayjs(row.expiry).format('YYYY-MM-DD HH:mm:ss') }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" width=\"250\" fixed=\"right\">\n          <template #default=\"scope\">\n            <el-button size=\"small\" @click=\"handleEdit(scope.row)\">编辑</el-button>\n            <el-upload\n              class=\"upload-button\"\n              :show-file-list=\"false\"\n              :http-request=\"(params) => handleUpload(params, scope.row)\"\n              accept=\"image/*\"\n            >\n              <el-button size=\"small\" type=\"primary\">上传图片</el-button>\n            </el-upload>\n            <el-button size=\"small\" type=\"danger\" @click=\"handleDelete(scope.row)\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-card>\n\n    <!-- 新增/编辑对话框 -->\n    <el-dialog\n      :title=\"dialogTitle\"\n      v-model=\"dialogVisible\"\n      width=\"500px\"\n      @close=\"handleDialogClose\"\n    >\n      <el-form\n        ref=\"formRef\"\n        :model=\"form\"\n        :rules=\"rules\"\n        label-width=\"100px\"\n      >\n        <el-form-item label=\"选择服务\" prop=\"serviceId\">\n          <el-select \n            v-model=\"selectedServiceId\" \n            placeholder=\"请选择服务\"\n            @change=\"handleServiceChange\"\n          >\n            <el-option\n              v-for=\"item in services\"\n              :key=\"item.id\"\n              :label=\"item.name\"\n              :value=\"item.id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"选择类别\" prop=\"categoryId\">\n          <el-select \n            v-model=\"selectedCategoryId\" \n            placeholder=\"请选择类别\"\n            :disabled=\"!selectedServiceId\"\n            @change=\"handleCategoryChange\"\n          >\n            <el-option\n              v-for=\"item in filteredCategories\"\n              :key=\"item.id\"\n              :label=\"item.name\"\n              :value=\"item.id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"选择品牌\" prop=\"brandId\">\n          <el-select \n            v-model=\"selectedBrandId\" \n            placeholder=\"请选择品牌\"\n            :disabled=\"!selectedCategoryId\"\n            @change=\"handleBrandChange\"\n          >\n            <el-option\n              v-for=\"item in filteredBrands\"\n              :key=\"item.id\"\n              :label=\"item.name\"\n              :value=\"item.id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"选择产品\" prop=\"productId\">\n          <el-select \n            v-model=\"form.productId\" \n            placeholder=\"请选择产品\"\n            :disabled=\"!selectedBrandId\"\n          >\n            <el-option\n              v-for=\"item in filteredInventoryProducts\"\n              :key=\"item.id\"\n              :label=\"item.name\"\n              :value=\"item.id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"标签\" prop=\"tags\">\n          <el-tag\n            v-for=\"tag in dynamicTags\"\n            :key=\"tag\"\n            closable\n            :disable-transitions=\"false\"\n            @close=\"handleTagClose(tag)\"\n          >\n            {{ tag }}\n          </el-tag>\n          <el-input\n            v-if=\"inputTagVisible\"\n            ref=\"tagInputRef\"\n            v-model=\"inputTagValue\"\n            class=\"tag-input\"\n            size=\"small\"\n            @keyup.enter=\"handleTagInputConfirm\"\n            @blur=\"handleTagInputConfirm\"\n          />\n          <el-button v-else class=\"button-new-tag\" size=\"small\" @click=\"showTagInput\">\n            + 添加标签\n          </el-button>\n        </el-form-item>\n        <el-form-item label=\"数量\" prop=\"quantity\">\n          <el-input-number\n            v-model=\"form.quantity\"\n            :min=\"0\"\n            :precision=\"0\"\n          />\n        </el-form-item>\n        <el-form-item label=\"总价格\" prop=\"price\">\n          <el-input-number \n            v-model=\"form.price\"\n            :precision=\"2\"\n            :step=\"0.1\"\n            :min=\"0\"\n          />\n        </el-form-item>\n        <el-form-item label=\"有效天数\" prop=\"validDays\">\n          <el-input-number \n            v-model=\"form.validDays\"\n            :min=\"1\"\n            :precision=\"0\"\n            :step=\"1\"\n            placeholder=\"请输入有效天数\"\n          />\n        </el-form-item>\n        <el-form-item label=\"截止时间\" prop=\"expiry\">\n          <el-date-picker\n            v-model=\"form.expiry\"\n            type=\"datetime\"\n            placeholder=\"选择时间\"\n            format=\"YYYY-MM-DD HH:mm:ss\"\n            value-format=\"YYYY-MM-DDTHH:mm:ss\"\n          />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"handleSubmit\">确定</el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\n  import { ref, computed, onMounted, nextTick } from 'vue'\n  import { ElMessage, ElMessageBox } from 'element-plus'\n  import type { FormInstance } from 'element-plus'\n  import { \n    getMPProducts, \n    getProducts,\n    getServices,\n    getCategories,\n    getBrands,\n    createMPProduct, \n    modifyMPProduct, \n    deleteMPProduct,\n    setProductImage,\n    getProductImages\n  } from '@/api/product'\n  import type { \n    MPProduct,\n    ProductImage,\n    Product,\n    Service,\n    Category,\n    Brand,\n    MPProductCreateParams, \n    MPProductUpdateParams \n  } from '@/api/product'\n  import dayjs from 'dayjs'\n\n  const activityProducts = ref<MPProduct[]>([])\n  const inventoryProducts = ref<Product[]>([])\n  const services = ref<Service[]>([])\n  const categories = ref<Category[]>([])\n  const brands = ref<Brand[]>([])\n  const loading = ref(false)\n  const dialogVisible = ref(false)\n  const dialogTitle = ref('')\n  const formRef = ref<FormInstance>()\n\n  // 标签相关\n  const dynamicTags = ref<string[]>([])\n  const inputTagVisible = ref(false)\n  const inputTagValue = ref('')\n  const tagInputRef = ref<HTMLInputElement>()\n\n  const form = ref<Partial<MPProductUpdateParams>>({\n    productId: undefined,\n    price: 0,\n    quantity: 0,\n    expiry: '',\n    validDays: 7,\n    tags: ''\n  })\n\n  const selectedServiceId = ref<number>()\n  const selectedCategoryId = ref<number>()\n  const selectedBrandId = ref<number>()\n\n  // 筛选变量\n  const filterStatus = ref('')  // 空字符串对应\"全部\"\n  const filterServiceId = ref('')  // 空字符串对应\"全部\"\n\n  // 添加商品图片数据\n  const productImages = ref<ProductImage[]>([])\n\n  // 添加排序配置\n  const sortConfig = ref({\n    prop: '',\n    order: ''\n  })\n\n  // 过滤计算属性\n  const filteredCategories = computed(() => {\n    if (!selectedServiceId.value) return []\n    return categories.value.filter(c => c.serviceId === selectedServiceId.value)\n  })\n\n  const filteredBrands = computed(() => {\n    if (!selectedCategoryId.value) return []\n    return brands.value.filter(b => b.categoryId === selectedCategoryId.value)\n  })\n\n  const filteredInventoryProducts = computed(() => {\n    if (!selectedBrandId.value) return []\n    return inventoryProducts.value.filter(p => p.brandId === selectedBrandId.value)\n  })\n\n  // 修改筛选逻辑，加入排序\n  const filteredProducts = computed(() => {\n    let result = [...activityProducts.value]\n    \n    // 按状态筛选\n    if (filterStatus.value !== '') {\n      result = result.filter(product => {\n        const isActive = dayjs(product.expiry).isAfter(dayjs())\n        if (filterStatus.value === 'active') {\n          return isActive\n        } else if (filterStatus.value === 'inactive') {\n          return !isActive\n        }\n        return true\n      })\n    }\n    \n    // 按服务类型名称筛选\n    if (filterServiceId.value !== '') {\n      result = result.filter(product => {\n        return getServiceName(product.productId) === filterServiceId.value\n      })\n    }\n    \n    // 应用排序\n    if (sortConfig.value.prop && sortConfig.value.order) {\n      const prop = sortConfig.value.prop\n      const isAsc = sortConfig.value.order === 'ascending'\n      \n      result.sort((a, b) => {\n        let comparison = 0\n        if (prop === 'quantity') {\n          comparison = a.quantity - b.quantity\n        } else if (prop === 'price') {\n          const priceA = typeof a.price === 'string' ? parseFloat(a.price) : a.price\n          const priceB = typeof b.price === 'string' ? parseFloat(b.price) : b.price\n          comparison = priceA - priceB\n        } else if (prop === 'validDays') {\n          comparison = a.validDays - b.validDays\n        } else if (prop === 'expiry') {\n          comparison = dayjs(a.expiry).isBefore(dayjs(b.expiry)) ? -1 : 1\n        }\n        \n        return isAsc ? comparison : -comparison\n      })\n    }\n    \n    return result\n  })\n\n  // 处理选择变化\n  const handleServiceChange = () => {\n    selectedCategoryId.value = undefined\n    selectedBrandId.value = undefined\n    form.value.productId = undefined\n  }\n\n  const handleCategoryChange = () => {\n    selectedBrandId.value = undefined\n    form.value.productId = undefined\n  }\n\n  const handleBrandChange = () => {\n    form.value.productId = undefined\n  }\n\n  // 标签相关方法\n  const showTagInput = () => {\n    inputTagVisible.value = true\n    nextTick(() => {\n      tagInputRef.value?.focus()\n    })\n  }\n\n  const handleTagInputConfirm = () => {\n    if (inputTagValue.value) {\n      if (!dynamicTags.value.includes(inputTagValue.value)) {\n        dynamicTags.value.push(inputTagValue.value)\n        updateFormTags()\n      }\n    }\n    inputTagVisible.value = false\n    inputTagValue.value = ''\n  }\n\n  const handleTagClose = (tag: string) => {\n    dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1)\n    updateFormTags()\n  }\n\n  const updateFormTags = () => {\n    form.value.tags = dynamicTags.value.join(' ')\n  }\n\n  const getTags = (tagsString: string): string[] => {\n    if (!tagsString) return []\n    return tagsString.split(' ').filter(tag => tag.trim() !== '')\n  }\n\n  const rules = {\n    productId: [\n      { required: true, message: '请选择产品', trigger: 'change' }\n    ],\n    price: [\n      { required: true, message: '请输入价格', trigger: 'blur' }\n    ],\n    quantity: [\n      { required: true, message: '请输入库存', trigger: 'blur' }\n    ],\n    expiry: [\n      { required: true, message: '请选择有效期', trigger: 'change' }\n    ],\n    validDays: [\n      { required: true, message: '请输入有效天数', trigger: 'blur' }\n    ]\n  }\n\n  const fetchAllData = async () => {\n    loading.value = true\n    try {\n      const [activityProductsRes, inventoryProductsRes, servicesRes, categoriesRes, brandsRes, imagesRes] = await Promise.all([\n        getMPProducts(),\n        getProducts(),\n        getServices(),\n        getCategories(),\n        getBrands(),\n        getProductImages()\n      ])\n      activityProducts.value = activityProductsRes.data\n      inventoryProducts.value = inventoryProductsRes.data\n      services.value = servicesRes.data\n      categories.value = categoriesRes.data\n      brands.value = brandsRes.data\n      productImages.value = imagesRes.data.map((img: ProductImage) => ({\n        ...img,\n        productId: parseInt(String(img.productId))\n      }))\n    } catch (error) {\n      ElMessage.error('获取数据失败')\n      console.error('获取数据失败:', error)\n    } finally {\n      loading.value = false\n    }\n  }\n\n  onMounted(fetchAllData)\n\n  const formatPrice = (price: number) => {\n    // 确保 price 是数字\n    const numPrice = typeof price === 'string' ? parseFloat(price) : price\n    \n    // 检查是否为有效数字\n    if (isNaN(numPrice)) {\n      return '¥0.00'\n    }\n    \n    return `¥ ${numPrice.toFixed(2)}`\n  }\n\n  const getProductName = (productId: number) => {\n    const product = inventoryProducts.value.find(p => p.id == productId)\n    return product?.name || '-'\n  }\n\n  const getServiceName = (productId: number) => {\n    const product = inventoryProducts.value.find(p => p.id == productId)\n    const service = services.value.find(s => s.id == product?.serviceId)\n    return service?.name || '-'\n  }\n\n  const handleAdd = () => {\n    dialogTitle.value = '新增商品'\n    form.value = {\n      productId: undefined,\n      price: 1,\n      quantity: 1,\n      expiry: dayjs().format('YYYY-MM-DDTHH:mm:ss'),\n      validDays: 7,\n      tags: ''\n    }\n    dynamicTags.value = []\n    selectedServiceId.value = undefined\n    selectedCategoryId.value = undefined\n    selectedBrandId.value = undefined\n    dialogVisible.value = true\n  }\n\n  const handleEdit = (row: MPProduct) => {\n    dialogTitle.value = '编辑产品'\n    const product = inventoryProducts.value.find(p => p.id == row.productId)\n    if (product) {\n      selectedBrandId.value = product.brandId\n      const brand = brands.value.find(b => b.id == product.brandId)\n      if (brand) {\n        selectedCategoryId.value = brand.categoryId\n        const category = categories.value.find(c => c.id == brand.categoryId)\n        if (category) {\n          selectedServiceId.value = category.serviceId\n        }\n      }\n    }\n    \n    form.value = {\n      id: row.id,\n      productId: row.productId,\n      price: row.price,\n      quantity: row.quantity,\n      expiry: row.expiry,\n      validDays: row.validDays,\n      tags: row.tags || ''\n    }\n    \n    // 设置标签数组\n    dynamicTags.value = getTags(row.tags || '')\n    \n    dialogVisible.value = true\n  }\n\n  const handleDelete = (row: MPProduct) => {\n    ElMessageBox.confirm(\n      '确定要删除这个产品吗？',\n      '警告',\n      {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning',\n      }\n    ).then(async () => {\n      try {\n        await deleteMPProduct(row.id)\n        await fetchAllData()\n        ElMessage.success('删除成功')\n      } catch (error) {\n        ElMessage.error('删除失败')\n      }\n    }).catch(() => {\n      // 点击取消按钮\n    })\n  }\n\n  const handleSubmit = async () => {\n    if (!formRef.value) return\n    \n    await formRef.value.validate(async (valid) => {\n      if (valid) {\n        try {\n          if (form.value.id) {\n            await modifyMPProduct(form.value as MPProductUpdateParams)\n          } else {\n            await createMPProduct(form.value as MPProductCreateParams)\n          }\n          dialogVisible.value = false\n          await fetchAllData()\n          ElMessage.success(form.value.id ? '编辑成功' : '新增成功')\n        } catch (error) {\n          ElMessage.error(form.value.id ? '编辑失败' : '新增失败')\n        }\n      }\n    })\n  }\n\n  const handleDialogClose = () => {\n    if (formRef.value) {\n      formRef.value.resetFields()\n    }\n    selectedServiceId.value = undefined\n    selectedCategoryId.value = undefined\n    selectedBrandId.value = undefined\n    dynamicTags.value = []\n    inputTagVisible.value = false\n    inputTagValue.value = ''\n  }\n\n  // 添加状态判断方法\n  const getStatusType = (expiry: string) => {\n    return dayjs(expiry).isAfter(dayjs()) ? 'success' : 'danger'\n  }\n\n  const getStatusText = (expiry: string) => {\n    return dayjs(expiry).isAfter(dayjs()) ? '在售' : '下架'\n  }\n\n  // 修改获取商品图片的方法\n  const getProductImage = (productId: number) => {\n    const image = productImages.value.find(img => img.productId == productId)\n    if (!image)\n      return new URL('@/assets/default.png', import.meta.url).href // 使用URL构造函数和import.meta.url来正确引用assets目录下的静态资源\n    return process.env.VUE_APP_API_BASE_URL + \"/productCover/\" + image?.fileName\n  }\n\n  // 添加上传处理方法\n  const handleUpload = async (params: { file: File }, row: MPProduct) => {\n    try {\n      // 检查文件大小是否超过5MB\n      if (params.file.size > 5 * 1024 * 1024) {\n        ElMessage.error('图片大小不能超过5MB')\n        return\n      }\n\n      await setProductImage(row.productId, params.file)\n      ElMessage.success('上传成功')\n      await fetchAllData()  // 重新加载数据以显示新图片\n    } catch (error) {\n      ElMessage.error('上传失败')\n      console.error('上传失败:', error)\n    }\n  }\n\n  // 为产品表格的相应列添加排序功能\n  const sortByQuantity = (a: MPProduct, b: MPProduct) => {\n    return a.quantity - b.quantity\n  }\n\n  const sortByPrice = (a: MPProduct, b: MPProduct) => {\n    // 确保价格是数字\n    const priceA = typeof a.price === 'string' ? parseFloat(a.price) : a.price\n    const priceB = typeof b.price === 'string' ? parseFloat(b.price) : b.price\n    return priceA - priceB\n  }\n\n  const sortByValidDays = (a: MPProduct, b: MPProduct) => {\n    return a.validDays - b.validDays\n  }\n\n  const sortByEndDate = (a: MPProduct, b: MPProduct) => {\n    return dayjs(a.expiry).isBefore(dayjs(b.expiry)) ? -1 : 1\n  }\n\n  // 处理排序\n  const handleSort = (column: { prop: string; order: string }) => {\n    sortConfig.value.prop = column.prop\n    sortConfig.value.order = column.order\n  }\n</script>\n\n<style scoped>\n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n  }\n\n  .header-operations {\n    display: flex;\n    align-items: center;\n    gap: 32px;\n  }\n\n  .filter-group {\n    display: flex;\n    gap: 16px;\n  }\n\n  .status-filter {\n    width: 80px;\n  }\n\n  .service-filter {\n    width: 110px;\n  }\n\n  .dialog-footer {\n    display: flex;\n    justify-content: flex-end;\n    gap: 12px;\n  }\n\n  .upload-button {\n    display: inline-block;\n    margin: 0 4px;\n  }\n\n  /* 调整表格操作列按钮间距 */\n  .el-button + .el-button {\n    margin-left: 4px;\n  }\n\n  /* 标签样式 */\n  .tags-container {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 4px;\n  }\n\n  .tag-item {\n    margin-right: 4px;\n  }\n\n  .tag-input {\n    width: 90px;\n    margin-left: 8px;\n    vertical-align: bottom;\n  }\n\n  .button-new-tag {\n    margin-left: 8px;\n    height: 32px;\n    padding-top: 0;\n    padding-bottom: 0;\n  }\n</style> ", "import script from \"./ProductView.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./ProductView.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./ProductView.vue?vue&type=style&index=0&id=71e289f9&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/.pnpm/vue-loader@17.4.2_@vue+compiler-sfc@3.5.13_vue@3.5.13_typescript@5.8.3__webpack@5.98.0/node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-71e289f9\"]])\n\nexport default __exports__", "import request from '@/utils/request'\n\n// 产品类型定义\n\nexport interface Service {\n  id: number\n  name: string\n}\n\nexport interface Category {\n  id: number\n  name: string\n  serviceId: number\n}\n\nexport interface Brand {\n  id: number\n  name: string\n  categoryId: number\n}\n\nexport interface Product {\n  id: number\n  name: string\n  price: number\n  quantity: number\n  factoryPrice: number\n  serviceId: number\n  categoryId: number\n  brandId: number\n  isService: boolean\n}\n\nexport interface MPProduct {\n  id: number\n  productId: number\n  tags: string\n  quantity: number\n  price: number\n  expiry: string\n  validDays: number\n}\n\nexport interface ProductImage {\n  productId: number\n  fileName: string\n}\n\n// 创建产品参数\nexport interface MPProductCreateParams {\n  productId: number\n  tags: string\n  quantity: number\n  price: number\n  expiry: string\n  validDays: number\n}\n\n// 更新产品参数\nexport interface MPProductUpdateParams extends MPProductCreateParams {\n  id: number\n}\n\n// API 方法\n\n\n\nexport const getServices = () => {\n  return request({\n    url: '/queryServices',\n    method: 'get'\n  })\n} \n\nexport const getCategories = () => {\n  return request({\n    url: '/queryCategories',\n    method: 'get'\n  })\n}\n\nexport const getBrands = () => {\n  return request({\n    url: '/queryBrands',\n    method: 'get'\n  })\n}\n\nexport const getProducts = () => {\n  return request({\n    url: '/queryProducts',\n    method: 'get'\n  })\n}\n\nexport const getMPProducts = () => {\n  return request({\n    url: '/queryMPProducts',\n    method: 'get'\n  })\n}\n\nexport const createMPProduct = (productVO: MPProductCreateParams) => {\n  return request({\n    url: '/createMPProduct',\n    method: 'post',\n    data: productVO\n  })\n}\n\nexport const modifyMPProduct = (mpProductVO: MPProductUpdateParams) => {\n  return request({\n    url: '/modifyMPProduct',\n    method: 'post',\n    data: mpProductVO\n  })\n}\n\nexport const deleteMPProduct = (mpProductId: number) => {\n  return request({\n    url: '/deleteMPProduct',\n    method: 'post',\n    params: { mpProductId }\n  })\n}\n\nexport const deleteMPProducts = (mpProductIds: number[]) => {\n  return request({\n    url: '/deleteMPProducts',\n    method: 'post',\n    params: { mpProductIds }\n  })\n}\n\nexport const setProductImage = (productId: number, image: File) => {\n  const formData = new FormData()\n  formData.append('productId', productId.toString())\n  formData.append('image', image)\n  return request({\n    url: '/setProductImage', \n    method: 'post',\n    data: formData,\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  })\n}\n\nexport const getProductImages = () => {\n  return request({\n    url: '/queryProductImages',\n    method: 'get'\n  })\n}\n"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_defineComponent", "__name", "setup", "__props", "activityProducts", "ref", "inventoryProducts", "services", "categories", "brands", "loading", "dialogVisible", "dialogTitle", "formRef", "dynamicTags", "inputTagVisible", "inputTagValue", "tagInputRef", "form", "productId", "undefined", "price", "quantity", "expiry", "validDays", "tags", "selectedServiceId", "selectedCategoryId", "selectedBrandId", "filterStatus", "filterServiceId", "productImages", "sortConfig", "prop", "order", "filteredCategories", "computed", "value", "filter", "c", "serviceId", "filteredBrands", "b", "categoryId", "filteredInventoryProducts", "p", "brandId", "filteredProducts", "result", "product", "isActive", "dayjs", "isAfter", "getServiceName", "isAsc", "sort", "a", "comparison", "priceA", "parseFloat", "priceB", "isBefore", "handleServiceChange", "handleCategoryChange", "handleBrandChange", "showTagInput", "nextTick", "focus", "handleTagInputConfirm", "includes", "push", "updateFormTags", "handleTagClose", "tag", "splice", "indexOf", "join", "getTags", "tagsString", "split", "trim", "rules", "required", "message", "trigger", "fetchAllData", "async", "activityProductsRes", "inventoryProductsRes", "servicesRes", "categoriesRes", "brandsRes", "imagesRes", "Promise", "all", "getMPProducts", "getProducts", "getServices", "getCategories", "getBrands", "getProductImages", "data", "map", "img", "parseInt", "String", "error", "ElMessage", "console", "onMounted", "formatPrice", "numPrice", "isNaN", "toFixed", "getProductName", "find", "id", "name", "service", "s", "handleAdd", "format", "handleEdit", "row", "brand", "category", "handleDelete", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "type", "then", "deleteMPProduct", "success", "catch", "handleSubmit", "validate", "valid", "modifyMPProduct", "createMPProduct", "handleDialogClose", "resetFields", "getStatusType", "getStatusText", "getProductImage", "image", "process", "fileName", "URL", "href", "handleUpload", "params", "file", "size", "setProductImage", "sortByQuantity", "sortByPrice", "sortByValidDays", "sortByEndDate", "handleSort", "column", "_ctx", "_cache", "_component_el_option", "_resolveComponent", "_component_el_select", "_component_el_button", "_component_el_table_column", "_component_el_image", "_component_el_tag", "_component_el_upload", "_component_el_table", "_component_el_card", "_component_el_form_item", "_component_el_input", "_component_el_input_number", "_component_el_date_picker", "_component_el_form", "_component_el_dialog", "_directive_loading", "_resolveDirective", "_openBlock", "_createElementBlock", "_createVNode", "header", "_withCtx", "_createElementVNode", "modelValue", "$event", "placeholder", "default", "label", "_", "_Fragment", "_renderList", "_createBlock", "key", "onClick", "_createTextVNode", "_withDirectives", "style", "onSortChange", "width", "src", "fit", "_toDisplayString", "index", "sortable", "_unref", "fixed", "scope", "accept", "title", "onClose", "footer", "ref_key", "model", "onChange", "item", "disabled", "closable", "onKeyup", "_with<PERSON><PERSON><PERSON>", "onBlur", "min", "precision", "step", "__exports__", "request", "url", "method", "productVO", "mpProductVO", "mpProductId", "formData", "FormData", "append", "toString", "headers"], "sourceRoot": ""}