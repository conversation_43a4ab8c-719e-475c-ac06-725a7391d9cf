package com.yzedulife.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.yzedulife.annotation.MembershipToken;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.common.domain.CommonErrorCode;
import com.yzedulife.common.util.JwtUtil;
import com.yzedulife.response.MPUserTokenResponse;
import com.yzedulife.response.Response;
import com.yzedulife.service.SmsService;
import com.yzedulife.service.dto.CodeDTO;
import com.yzedulife.service.dto.MembershipDTO;
import com.yzedulife.service.dto.TransactionRecordDTO;
import com.yzedulife.service.service.AdminService;
import com.yzedulife.service.service.CodeService;
import com.yzedulife.service.service.MembershipService;
import com.yzedulife.service.service.TransactionRecordService;
import com.yzedulife.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;

@RestController
@Tag(name = "微信 接口")
public class WxUserController {

    @Autowired
    private CodeService codeService;
    @Autowired
    private MembershipService membershipService;
    @Autowired
    private TransactionRecordService transactionRecordService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private WxMaService wxMaService;
    @Autowired
    private QrLoginCache qrLoginCache;
    @Autowired
    private AdminService adminService;

    @Operation(summary = "授权获取手机号")
    @GetMapping("/getPhone")
    public Response getPhone(@RequestParam String phoneCode,
                             @RequestParam String loginCode) {
        WxMaJscode2SessionResult session;
        try {
            // 获取openid
            session = wxMaService.getUserService().getSessionInfo(loginCode);
            String openid = session.getOpenid();
            // 获取手机号
            WxMaPhoneNumberInfo phoneNoInfo = wxMaService.getUserService().getPhoneNumber(phoneCode);
            String phoneNumber = phoneNoInfo.getPhoneNumber();
            String token = JwtUtil.createToken(phoneNumber, openid);
            return Response.success().data(new MPUserTokenResponse(openid, phoneNumber, token));
        } catch (Exception e) {
            return Response.error().msg(e.toString());
        }
    }

    @Operation(summary = "发送验证码")
    @PostMapping("/sendCode")
    public Response sendCode(@RequestParam String phone) {
        Random random = new Random();
        // 生成验证码
        String code = String.valueOf(100000 + random.nextInt(900000));
        codeService.create(phone, code, 120L);

        // 发送验证码
        smsService.sendAliCode(phone, code);
        return Response.success();
    }

    @Operation(summary ="登录账号")
    @PostMapping(value = "/login")
    public Response LoginUser(@RequestParam String phone,
                              @RequestParam String code,
                              @RequestParam String loginCode) throws Exception {
        CodeDTO codeDTO = codeService.query(phone);
        // 验证码不存在或已过期
        if (codeDTO == null || codeDTO.getExpiry().isBefore(LocalDateTime.now()))
            throw new BusinessException(CommonErrorCode.E_500002);

        // 验证码错误
        if (!codeDTO.getCode().equals(code))
            throw new BusinessException(CommonErrorCode.E_500003);

        // 获取openid
        WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(loginCode);
        String openid = session.getOpenid();

        String token = JwtUtil.createToken(phone, openid);
        return Response.success().data(new MPUserTokenResponse(openid, phone, token)).msg("登录成功");
    }

    @MembershipToken
    @Operation(summary = "验证会员身份")
    @GetMapping("/verifyMembership")
    public Response verifyMembership() {
        String phone = SecurityUtil.getPhone();
        if (phone == null)
            throw new BusinessException(CommonErrorCode.E_300001);
        return Response.success().data(membershipService.isExist(phone));
    }

    @MembershipToken
    @Operation(summary = "获取会员余额")
    @GetMapping("/getMembershipBalance")
    public Response getMembershipBalance() {
        String phone = SecurityUtil.getPhone();
        if (phone == null)
            throw new BusinessException(CommonErrorCode.E_300001);
        return Response.success().data(membershipService.query(phone).getBalance());
    }

    @MembershipToken
    @Operation(summary = "获取会员余额记录")
    @GetMapping("/getMembershipBalanceRecord")
    public Response getMembershipBalanceRecord() {
        String phone = SecurityUtil.getPhone();
        MembershipDTO membershipDTO = membershipService.query(phone);
        if (phone == null)
            throw new BusinessException(CommonErrorCode.E_300001);
        List<TransactionRecordDTO> res = transactionRecordService.query(membershipDTO.getId());
        return Response.success().data(res);
    }

    @Operation(summary = "请求扫码登录、查看扫码状态")
    @PostMapping("/checkLogin")
    public Response checkLogin(@RequestParam String deviceId) {
        QrLoginCache.LoginInfo loginInfo = qrLoginCache.checkStatus(deviceId);
        if (loginInfo == null || loginInfo.getToken() == null)
            return Response.success().data(false);
        String token = loginInfo.getToken();
        String phone = JwtUtil.getPhone(token);
        if (!adminService.isExist(phone))
            throw new BusinessException(CommonErrorCode.E_NO_PERMISSION);
        return Response.success().data(token);
    }

    @Operation
    @PostMapping("/confirmLogin")
    @MembershipToken
    public Response confirmLogin(@RequestParam String deviceId) {
        String token = SecurityUtil.getToken();
        qrLoginCache.updateToken(deviceId, token);
        return Response.success();
    }
}
