package com.yzedulife.controller;

import com.yzedulife.annotation.MembershipToken;
import com.yzedulife.service.dto.MembershipDTO;
import com.yzedulife.service.dto.TransactionRecordDTO;
import com.yzedulife.response.Response;
import com.yzedulife.service.service.MembershipService;
import com.yzedulife.service.service.TransactionRecordService;
import com.yzedulife.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Tag(name = "交易记录 接口")
public class TransactionRecordController {
    @Autowired
    private MembershipService membershipService;
    @Autowired
    private TransactionRecordService transactionRecordService;

    @MembershipToken
    @Operation(summary = "获取交易记录")
    @GetMapping("/queryTransactionRecord")
    public Response queryTransactionRecord() {
        String phone = SecurityUtil.getPhone();
        MembershipDTO membershipDTO = membershipService.query(phone);
        List<TransactionRecordDTO> transactionRecordDTOs = transactionRecordService.query(membershipDTO.getId());
        return Response.success().data(transactionRecordDTOs);
    }
}
