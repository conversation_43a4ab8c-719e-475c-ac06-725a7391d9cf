package com.yzedulife.convert;

import com.yzedulife.service.dto.CategoryDTO;
import com.yzedulife.response.CategoryResponse;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CategoryConvert {
    CategoryConvert INSTANCE = Mappers.getMapper(CategoryConvert.class);

    CategoryResponse dto2res(CategoryDTO categoryDTO);
    List<CategoryResponse> dto2resBatch(List<CategoryDTO> categoryDTOs);
}
