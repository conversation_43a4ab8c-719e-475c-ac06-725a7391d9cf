package com.yzedulife;

import com.yzedulife.service.dto.MPOrderDTO;
import com.yzedulife.service.service.MPOrderService;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class TestApplication {

    @Autowired
    private MPOrderService mpOrderService;
    @Autowired
    private WxPayService wxPayService;

    @SneakyThrows
    @Test
    public void test() {
        refundAll(100031L);
    }

    private void refundAll(Long mpOrderId) {
        MPOrderDTO mpOrderDTO = mpOrderService.query(mpOrderId);
        wxPayService.refundAll(mpOrderDTO);
    }

}
