"use strict";(self["webpackChunkadmin_web"]=self["webpackChunkadmin_web"]||[]).push([[714],{1714:function(e,a,t){t.r(a),t.d(a,{default:function(){return h}});t(5226);var n=t(4922),c=t(1402),r=t(5201),s=t(7415),l=t(9077);const o=e=>(0,l.A)({url:"/checkLogin",method:"post",params:{deviceId:e}}),d={class:"login-container"},i={class:"login-content"},u={class:"qrcode-box"},v=["src"];var p=(0,n.pM)({__name:"LoginView",setup(e){const a=(0,r.rd)(),t=(0,c.KR)("");let l=null;const p=async()=>{try{const e={data:{deviceId:"device_"+Date.now()}};t.value=e.data.deviceId,k()}catch(e){s.nk.error("获取二维码失败，请刷新页面重试"),console.error("获取二维码失败:",e)}},k=()=>{l&&clearInterval(l),setTimeout((()=>{l&&(clearInterval(l),l=null,p())}),12e4),l=window.setInterval((async()=>{try{const e=await o(t.value);if(!1===e.data)return;e.data&&(null!==l&&(clearInterval(l),l=null),localStorage.setItem("token",e.data),s.nk.success("登录成功"),a.push("/dashboard"))}catch(e){console.error("检查登录状态失败:",e)}}),1e3)};return(0,n.sV)((()=>{p()})),(0,n.hi)((()=>{l&&(clearInterval(l),l=null)})),(e,a)=>((0,n.uX)(),(0,n.CE)("div",d,[(0,n.Lk)("div",i,[(0,n.Lk)("div",u,[t.value?((0,n.uX)(),(0,n.CE)("img",{key:0,src:`https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(t.value)}&size=200x200&margin=10`,alt:"登录二维码",class:"qrcode-img"},null,8,v)):(0,n.Q3)("",!0)]),a[0]||(a[0]=(0,n.Lk)("p",{class:"scan-tip"},"请使用微信小程序扫一扫登录",-1))])]))}}),k=t(9996);const m=(0,k.A)(p,[["__scopeId","data-v-3485a2cd"]]);var h=m}}]);
//# sourceMappingURL=714.612fdf09.js.map