{"version": 3, "file": "js/28.df85b80c.js", "mappings": "oSAGA,MAAMA,EAAa,CAAEC,MAAO,cACtBC,EAAa,CAAED,MAAO,eACtBE,EAAa,CAAEF,MAAO,qBACtBG,EAAa,CAAEH,MAAO,gBCqHtBI,EAAsB,gBACtBC,EAAmB,IDlGzB,OAA4BC,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,YACRC,KAAAA,CAAMC,GCkGR,MAAMC,GAAUC,EAAAA,EAAAA,KAAI,GACdC,GAASD,EAAAA,EAAAA,IAAa,IACtBE,GAAWF,EAAAA,EAAAA,IAAe,IAC1BG,GAAWH,EAAAA,EAAAA,IAAe,IAG1BI,GAAoBJ,EAAAA,EAAAA,IAAmB,MACvCK,GAA2BL,EAAAA,EAAAA,IAAmB,MACpD,IAAIM,EAA8B,KAGlC,MAAMC,GAAeP,EAAAA,EAAAA,IAAI,IACnBQ,GAAkBR,EAAAA,EAAAA,IAAI,IACtBS,GAAcT,EAAAA,EAAAA,IAAI,IAOlBU,EAAYC,MAAOC,GAAgB,KACnCA,IACFb,EAAQc,OAAQ,GAElB,IACE,MAAOC,EAAWC,EAAaC,SAAqBC,QAAQC,IAAI,EAC9DC,EAAAA,EAAAA,OACAC,EAAAA,EAAAA,OACAC,EAAAA,EAAAA,QAGIC,EAAyBR,EAAUS,MAAQ,GACjD,IAAIC,EAAe,EACfF,EAAcG,OAAS,IACzBD,EAAeE,KAAKC,OAAOL,EAAcM,KAAIC,GAAKC,OAAOD,EAAEE,MAAKC,QAAOD,IAAOE,MAAMF,OAItF,MAAMG,EAAqB9B,EAAkBS,MAC7C,IAwBIsB,EAxBAC,EAAwBF,GAED,OAAvBA,GAEOV,EAAeU,KADtBE,EAAwBZ,GAMxBY,IAA0BF,IAC1B9B,EAAkBS,MAAQuB,EAC1BC,aAAaC,QAAQ7C,EAAqB8C,OAAOH,IACjDI,QAAQC,IAAI,oCAAoCL,MAMhDxB,GAAoD,OAAnCP,EAAyBQ,QAC1CR,EAAyBQ,MAAQuB,EACjCI,QAAQC,IAAI,8DAA8DpC,EAAyBQ,UAKvGsB,EAAkBb,EAAcM,KAAIc,IAChC,MAAMC,EAAiBb,OAAOY,EAAMX,IACpC,IAAIa,GAAQ,EAKZ,OAHuC,OAAnCvC,EAAyBQ,OAAmBoB,MAAMU,KAClDC,EAAQD,EAAiBtC,EAAyBQ,OAE/C,IAAK6B,EAAOE,MAAOA,EAAO,IAGrC3C,EAAOY,MAAQsB,EACfjC,EAASW,MAAQE,EAAYQ,KAC7BpB,EAASU,MAAQG,EAAYO,IAC/B,CAAE,MAAOsB,GACPL,QAAQK,MAAM,UAAWA,GACrBjC,GACAkC,EAAAA,GAAUD,MAAM,WAEtB,CAAE,QACIjC,IACFb,EAAQc,OAAQ,EAEpB,IAGFkC,EAAAA,EAAAA,KAAU,KAER,MAAMC,EAAiBX,aAAaY,QAAQxD,GAC5C,GAAIuD,EAAgB,CAClB,MAAME,EAAWC,SAASH,EAAgB,IACrCf,MAAMiB,GAIPb,aAAae,WAAW3D,IAH1BW,EAAkBS,MAAQqC,EAC1B7C,EAAyBQ,MAAQqC,EAKrC,CAGAV,QAAQC,IAAI,2DAA2DrC,EAAkBS,SACzF2B,QAAQC,IAAI,uDAAuDpC,EAAyBQ,SAG5FH,GAAU,GAGNJ,GACF+C,cAAc/C,GAGhBA,EAAegD,aAAY,KACvB5C,GAAU,EAAM,GACjBhB,EAAiB,IAGtB,MAAM6D,EAAiBC,IACrB,OAAQA,GACN,IAAK,UACH,MAAO,UACT,IAAK,SACH,MAAO,UACT,IAAK,OACH,MAAO,UACT,IAAK,UACH,MAAO,OACT,IAAK,WACH,MAAO,SACT,QACE,MAAO,OACX,EAIIC,EAAkBC,IACtB,MAAMC,EAAUzD,EAASW,MAAM+C,MAAKC,GAAKA,EAAE9B,IAAM2B,IAC3CI,EAAU3D,EAASU,MAAM+C,MAAKG,GAAKA,EAAEhC,IAAM4B,GAASK,YAC1D,OAAOF,GAASG,MAAQ,GAAE,EAGtBC,EAAkBR,IACtB,MAAMC,EAAUzD,EAASW,MAAM+C,MAAKC,GAAKA,EAAE9B,IAAM2B,IAC/C,OAAOC,GAASM,MAAQ,GAAE,EAGxBE,EAAeC,IAEnB,MAAMC,EAA4B,kBAAVD,EAAqBE,WAAWF,GAASA,EAGjE,OAAInC,MAAMoC,GACD,QAGF,KAAKA,EAASE,QAAQ,IAAG,EAI5BC,EAAe7D,UACnB,UACQ8D,EAAAA,EAAaC,QAAQ,YAAa,KAAM,CAC5CC,kBAAmB,KACnBC,iBAAkB,KAClBC,KAAM,kBAGFC,EAAAA,EAAAA,IAAYC,EAAIhD,IACtBe,EAAAA,GAAUkC,QAAQ,cACZtE,GACR,CAAE,MAAOmC,GACO,WAAVA,IACFC,EAAAA,GAAUD,MAAM,QAChBL,QAAQK,MAAM,QAASA,GAE3B,GAIIoC,EAA0BA,CAACC,EAAoBC,KACnD,MAAMC,EAAaF,EAAEG,SAAWH,EAAEI,aAC5BC,EAAaJ,EAAEE,SAAWF,EAAEG,aAClC,OAAOF,EAAaG,CAAS,EAIzBC,EAAcA,CAACN,EAAoBC,KAEvC,MAAMM,EAA4B,kBAAZP,EAAEd,MAAqBE,WAAWY,EAAEd,OAASc,EAAEd,MAC/DsB,EAA4B,kBAAZP,EAAEf,MAAqBE,WAAWa,EAAEf,OAASe,EAAEf,MACrE,OAAOqB,EAASC,CAAK,EAIjBC,EAAaA,CAACT,EAAoBC,IAC/BS,IAAMV,EAAEW,MAAMC,UAAYF,IAAMT,EAAEU,MAAMC,UAI3CC,EAAeA,CAACb,EAAoBC,IACjCS,IAAMV,EAAEc,QAAQF,UAAYF,IAAMT,EAAEa,QAAQF,UAI/CG,GAAiBC,EAAAA,EAAAA,KAAS,KAC9B,IAAIC,EAAS,IAAIlG,EAAOY,OAGxB,GAAiC,KAA7BJ,EAAYI,MAAMuF,OAAe,CACnC,MAAMC,EAAc5F,EAAYI,MAAMuF,OAAOE,cAC7CH,EAASA,EAAOnE,QAAOU,GACdA,EAAM6D,OAAS7D,EAAM6D,MAAMC,WAAWF,cAAcG,SAASJ,IAExE,CAgBA,MAb2B,KAAvB9F,EAAaM,QACfsF,EAASA,EAAOnE,QAAOU,GACdA,EAAMc,SAAWjD,EAAaM,SAKX,KAA1BL,EAAgBK,QAClBsF,EAASA,EAAOnE,QAAOU,GACde,EAAef,EAAMgB,aAAelD,EAAgBK,SAIxDsF,CAAK,IAIRO,EAAoBA,EAAG3B,SACvBA,EAAInC,MACC,sBAEF,GDzFT,OC6FA+D,EAAAA,EAAAA,KAAgB,KACVrG,IACF+C,cAAc/C,GACdA,EAAe,KACjB,IDjGK,CAACsG,EAAUC,KAChB,MAAMC,GAAqBC,EAAAA,EAAAA,IAAkB,WACvCC,GAAsBD,EAAAA,EAAAA,IAAkB,YACxCE,GAAuBF,EAAAA,EAAAA,IAAkB,aACzCG,GAAuBH,EAAAA,EAAAA,IAAkB,aACzCI,GAA6BJ,EAAAA,EAAAA,IAAkB,mBAC/CK,GAAoBL,EAAAA,EAAAA,IAAkB,UACtCM,GAAuBN,EAAAA,EAAAA,IAAkB,aACzCO,GAAsBP,EAAAA,EAAAA,IAAkB,YACxCQ,GAAqBR,EAAAA,EAAAA,IAAkB,WACvCS,GAAqBC,EAAAA,EAAAA,IAAkB,WAE7C,OAAQC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOvI,EAAY,EAC3DwI,EAAAA,EAAAA,IAAaL,EAAoB,CAAElI,MAAO,YAAc,CACtDwI,QAAQC,EAAAA,EAAAA,KAAS,IAAM,EACrBC,EAAAA,EAAAA,IAAoB,MAAOzI,EAAY,CACrCuH,EAAO,KAAOA,EAAO,IAAKkB,EAAAA,EAAAA,IAAoB,OAAQ,KAAM,QAAS,KACrEA,EAAAA,EAAAA,IAAoB,MAAOxI,EAAY,EACrCwI,EAAAA,EAAAA,IAAoB,MAAOvI,EAAY,EACrCoI,EAAAA,EAAAA,IAAaZ,EAAqB,CAChCgB,WAAYvH,EAAYI,MACxB,sBAAuBgG,EAAO,KAAOA,EAAO,GAAMoB,GAAkBxH,EAAaI,MAAQoH,GACzFC,YAAa,MACbC,UAAW,GACX9I,MAAO,4BACN,CACD+I,QAAQN,EAAAA,EAAAA,KAAS,IAAM,EACrBF,EAAAA,EAAAA,IAAad,EAAoB,KAAM,CACrCuB,SAASP,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,KAAaU,EAAAA,EAAAA,IAAOC,EAAAA,YAEtBC,EAAG,OAGPA,EAAG,GACF,EAAG,CAAC,gBACPZ,EAAAA,EAAAA,IAAaV,EAAsB,CACjCc,WAAYzH,EAAaM,MACzB,sBAAuBgG,EAAO,KAAOA,EAAO,GAAMoB,GAAkB1H,EAAcM,MAAQoH,GAC1FC,YAAa,KACbC,UAAW,GACX9I,MAAO,6BACN,CACDgJ,SAASP,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAaX,EAAsB,CACjCwB,MAAO,KACP5H,MAAO,MAET+G,EAAAA,EAAAA,IAAaX,EAAsB,CACjCwB,MAAO,MACP5H,MAAO,aAET+G,EAAAA,EAAAA,IAAaX,EAAsB,CACjCwB,MAAO,MACP5H,MAAO,YAET+G,EAAAA,EAAAA,IAAaX,EAAsB,CACjCwB,MAAO,MACP5H,MAAO,UAET+G,EAAAA,EAAAA,IAAaX,EAAsB,CACjCwB,MAAO,MACP5H,MAAO,aAET+G,EAAAA,EAAAA,IAAaX,EAAsB,CACjCwB,MAAO,MACP5H,MAAO,gBAGX2H,EAAG,GACF,EAAG,CAAC,gBACPZ,EAAAA,EAAAA,IAAaV,EAAsB,CACjCc,WAAYxH,EAAgBK,MAC5B,sBAAuBgG,EAAO,KAAOA,EAAO,GAAMoB,GAAkBzH,EAAiBK,MAAQoH,GAC7FC,YAAa,KACbC,UAAW,GACX9I,MAAO,8BACN,CACDgJ,SAASP,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAaX,EAAsB,CACjCwB,MAAO,KACP5H,MAAO,OAER6G,EAAAA,EAAAA,KAAW,IAAOC,EAAAA,EAAAA,IAAoBe,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAYxI,EAASU,OAAQiD,KAC3E4D,EAAAA,EAAAA,OAAckB,EAAAA,EAAAA,IAAa3B,EAAsB,CACvD4B,IAAK/E,EAAQ/B,GACb0G,MAAO3E,EAAQG,KACfpD,MAAOiD,EAAQG,MACd,KAAM,EAAG,CAAC,QAAS,aACpB,SAENuE,EAAG,GACF,EAAG,CAAC,wBAKfH,SAASP,EAAAA,EAAAA,KAAS,IAAM,EACtBgB,EAAAA,EAAAA,MAAiBpB,EAAAA,EAAAA,OAAckB,EAAAA,EAAAA,IAAatB,EAAqB,CAC/D/F,KAAM0E,EAAepF,MACrBkI,MAAO,CAAC,MAAQ,QAChB,iBAAkBrC,GACjB,CACD2B,SAASP,EAAAA,EAAAA,KAAS,IAAM,EACtBF,EAAAA,EAAAA,IAAaT,EAA4B,CACvC6B,KAAM,KACNP,MAAO,MACPQ,MAAO,SAETrB,EAAAA,EAAAA,IAAaT,EAA4B,CACvCsB,MAAO,KACPQ,MAAO,OACN,CACDZ,SAASP,EAAAA,EAAAA,KAAS,EAAG/C,SAAU,EAC7B6C,EAAAA,EAAAA,IAAaR,EAAmB,CAC9BvC,KAAMtB,EAAcwB,EAAIvB,SACvB,CACD6E,SAASP,EAAAA,EAAAA,KAAS,IAAM,EACtBoB,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,IAAgC,YAAfpE,EAAIvB,OAAuB,MAClD,WAAfuB,EAAIvB,OAAsB,MACX,SAAfuB,EAAIvB,OAAoB,MACT,YAAfuB,EAAIvB,OAAuB,MACZ,aAAfuB,EAAIvB,OAAwB,MAAQ,MAAO,MAEzCgF,EAAG,GACF,KAAM,CAAC,YAEZA,EAAG,KAELZ,EAAAA,EAAAA,IAAaT,EAA4B,CACvCsB,MAAO,OACPQ,MAAO,OACN,CACDZ,SAASP,EAAAA,EAAAA,KAAS,EAAG/C,SAAU,EAC7BmE,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,IAAiB1F,EAAesB,EAAIrB,YAAa,MAEpE8E,EAAG,KAELZ,EAAAA,EAAAA,IAAaT,EAA4B,CACvC6B,KAAM,YACNP,MAAO,MACPQ,MAAO,SAETrB,EAAAA,EAAAA,IAAaT,EAA4B,CACvCsB,MAAO,OACP,YAAa,OACZ,CACDJ,SAASP,EAAAA,EAAAA,KAAS,EAAG/C,SAAU,EAC7BmE,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,IAAiBjF,EAAea,EAAIrB,YAAa,MAEpE8E,EAAG,KAELZ,EAAAA,EAAAA,IAAaT,EAA4B,CACvCsB,MAAO,KACPQ,MAAO,MACPG,SAAU,GACV,cAAenE,GACd,CACDoD,SAASP,EAAAA,EAAAA,KAAS,EAAG/C,SAAU,EAC7BmE,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,IAAiBpE,EAAIM,SAAWN,EAAIO,cAAgB,KAAM6D,EAAAA,EAAAA,IAAiBpE,EAAIM,UAAW,MAE7GmD,EAAG,KAELZ,EAAAA,EAAAA,IAAaT,EAA4B,CACvCsB,MAAO,MACPQ,MAAO,MACPG,SAAU,GACV,cAAe5D,GACd,CACD6C,SAASP,EAAAA,EAAAA,KAAS,EAAG/C,SAAU,EAC7BmE,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,IAAiBhF,EAAYY,EAAIX,QAAS,MAE7DoE,EAAG,KAELZ,EAAAA,EAAAA,IAAaT,EAA4B,CACvC6B,KAAM,QACNP,MAAO,KACPQ,MAAO,SAETrB,EAAAA,EAAAA,IAAaT,EAA4B,CACvCsB,MAAO,OACPQ,MAAO,MACPG,SAAU,GACV,cAAezD,GACd,CACD0C,SAASP,EAAAA,EAAAA,KAAS,EAAG/C,SAAU,EAC7BmE,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,KAAiBb,EAAAA,EAAAA,IAAO1C,IAAP0C,CAAcvD,EAAIc,MAAMwD,OAAO,wBAAyB,MAE5Fb,EAAG,KAELZ,EAAAA,EAAAA,IAAaT,EAA4B,CACvCsB,MAAO,OACPQ,MAAO,MACPG,SAAU,GACV,cAAerD,GACd,CACDsC,SAASP,EAAAA,EAAAA,KAAS,EAAG/C,SAAU,EAC7BmE,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,KAAiBb,EAAAA,EAAAA,IAAO1C,IAAP0C,CAAcvD,EAAIiB,QAAQqD,OAAO,wBAAyB,MAE9Fb,EAAG,KAELZ,EAAAA,EAAAA,IAAaT,EAA4B,CACvCsB,MAAO,KACPQ,MAAO,MACPK,MAAO,SACN,CACDjB,SAASP,EAAAA,EAAAA,KAAUyB,GAAU,EAC3B3B,EAAAA,EAAAA,IAAaP,EAAsB,CACjCmC,KAAM,QACN3E,KAAM,SACN4E,QAAUxB,GAAiBzD,EAAa+E,EAAMxE,MAC7C,CACDsD,SAASP,EAAAA,EAAAA,KAAS,IAAMjB,EAAO,KAAOA,EAAO,GAAK,EAChDqC,EAAAA,EAAAA,IAAiB,UAEnBV,EAAG,GACF,KAAM,CAAC,eAEZA,EAAG,OAGPA,EAAG,GACF,EAAG,CAAC,UAAW,CAChB,CAAChB,EAAoBzH,EAAQc,YAGjC2H,EAAG,KAEL,CAEJ,I,UErfA,MAAMkB,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O,8GCoBO,MAAMvI,EAAYR,UACvB,MAAMgJ,QAAiBC,EAAAA,EAAAA,GAAyB,CAC9CC,IAAK,oBACLC,OAAQ,QAIV,MAAO,IACFH,EACHpI,KAAMoI,EAASpI,KAAKK,KAAIc,IAAS,CAC/BX,GAAIoB,SAAST,EAAMX,IACnB2B,UAAWP,SAAST,EAAMgB,WAC1B2B,SAAUlC,SAAST,EAAM2C,UACzBC,aAAcnC,SAAST,EAAM4C,cAC7BlB,MAAOE,WAAW5B,EAAM0B,OACxBmC,MAAO7D,EAAM6D,MACbV,KAAMnD,EAAMmD,KACZG,OAAQtD,EAAMsD,OACdxC,OAAQd,EAAMc,WAEjB,EAWUsB,EAAeiF,IACnBH,EAAAA,EAAAA,GAAQ,CACbC,IAAK,iBACLC,OAAQ,OACRE,OAAQ,CAAED,c,8RCIP,MAAM1I,EAAcA,KAClBuI,EAAAA,EAAAA,GAAQ,CACbC,IAAK,iBACLC,OAAQ,QAICG,EAAgBA,KACpBL,EAAAA,EAAAA,GAAQ,CACbC,IAAK,mBACLC,OAAQ,QAICI,EAAYA,KAChBN,EAAAA,EAAAA,GAAQ,CACbC,IAAK,eACLC,OAAQ,QAIC1I,EAAcA,KAClBwI,EAAAA,EAAAA,GAAQ,CACbC,IAAK,iBACLC,OAAQ,QAICK,EAAgBA,KACpBP,EAAAA,EAAAA,GAAQ,CACbC,IAAK,mBACLC,OAAQ,QAICM,EAAmBC,IACvBT,EAAAA,EAAAA,GAAQ,CACbC,IAAK,mBACLC,OAAQ,OACRvI,KAAM8I,IAIGC,EAAmBC,IACvBX,EAAAA,EAAAA,GAAQ,CACbC,IAAK,mBACLC,OAAQ,OACRvI,KAAMgJ,IAIGC,EAAmBC,IACvBb,EAAAA,EAAAA,GAAQ,CACbC,IAAK,mBACLC,OAAQ,OACRE,OAAQ,CAAES,iBAYDC,EAAkBA,CAAChH,EAAmBiH,KACjD,MAAMC,EAAW,IAAIC,SAGrB,OAFAD,EAASE,OAAO,YAAapH,EAAU8C,YACvCoE,EAASE,OAAO,QAASH,IAClBf,EAAAA,EAAAA,GAAQ,CACbC,IAAK,mBACLC,OAAQ,OACRvI,KAAMqJ,EACNG,QAAS,CACP,eAAgB,wBAElB,EAGSC,EAAmBA,KACvBpB,EAAAA,EAAAA,GAAQ,CACbC,IAAK,sBACLC,OAAQ,O", "sources": ["webpack://admin-web/./src/views/order/OrderView.vue?cbd0", "webpack://admin-web/./src/views/order/OrderView.vue", "webpack://admin-web/./src/views/order/OrderView.vue?c66b", "webpack://admin-web/./src/api/order.ts", "webpack://admin-web/./src/api/product.ts"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, unref as _unref, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\"\n\nconst _hoisted_1 = { class: \"order-view\" }\nconst _hoisted_2 = { class: \"card-header\" }\nconst _hoisted_3 = { class: \"header-operations\" }\nconst _hoisted_4 = { class: \"filter-group\" }\n\nimport { ref, onMounted, computed, onBeforeUnmount } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { getOrders, deleteOrder } from '@/api/order'\nimport { getProducts, getServices } from '@/api/product'\nimport type { Order } from '@/api/order'\nimport type { Product, Service } from '@/api/product'\nimport dayjs from 'dayjs'\nimport { Search } from '@element-plus/icons-vue'\n\n// 新增常量\nconst LATEST_ORDER_ID_KEY = 'latestOrderId';\nconst POLLING_INTERVAL = 60000; // 60 秒\n\ninterface OrderWithStatus extends Order {\n  isNew?: boolean;\n}\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'OrderView',\n  setup(__props) {\n\nconst loading = ref(false)\nconst orders = ref<Order[]>([])\nconst products = ref<Product[]>([])\nconst services = ref<Service[]>([])\n\n// 新增状态变量\nconst latestSeenOrderId = ref<number | null>(null);\nconst initialLatestSeenOrderId = ref<number | null>(null); // 新增：存储会话初始ID\nlet pollingTimer: number | null = null;\n\n// 筛选变量的默认值\nconst filterStatus = ref('')  // 空字符串对应\"全部\"\nconst filterServiceId = ref('')  // 空字符串对应\"全部\"\nconst filterPhone = ref('')  // 新增：手机号过滤\n\n// 扩展 Order 类型以包含 isNew\nconst fetchData = async (isInitialLoad = false) => {\n  if (isInitialLoad) {\n    loading.value = true\n  }\n  try {\n    const [ordersRes, productsRes, servicesRes] = await Promise.all([\n      getOrders(),\n      getProducts(),\n      getServices()\n    ])\n\n    const fetchedOrders: Order[] = ordersRes.data || [];\n    let currentMaxId = 0;\n    if (fetchedOrders.length > 0) {\n      currentMaxId = Math.max(...fetchedOrders.map(o => Number(o.id)).filter(id => !isNaN(id)));\n    }\n\n    // --- 更新持久化最新 ID (latestSeenOrderId) ---\n    const persistentLatestId = latestSeenOrderId.value;\n    let newPersistentLatestId = persistentLatestId;\n\n    if (persistentLatestId === null) { // 初始化\n        newPersistentLatestId = currentMaxId;\n    } else if (currentMaxId > persistentLatestId) { // 更新\n        newPersistentLatestId = currentMaxId;\n    }\n\n    // 如果值改变或被初始化，则更新 ref 和 localStorage\n    if (newPersistentLatestId !== persistentLatestId) {\n        latestSeenOrderId.value = newPersistentLatestId;\n        localStorage.setItem(LATEST_ORDER_ID_KEY, String(newPersistentLatestId));\n        console.log(`Updated persistent latest ID to: ${newPersistentLatestId}`);\n    }\n\n    // --- (新增逻辑) 处理首次加载且localStorage为空的情况，设置会话初始ID ---\n    // 只有在 initialLatestSeenOrderId 尚未被 onMounted 设置（即localStorage为空或无效）\n    // 且这是首次加载时，才在这里设置它\n    if (isInitialLoad && initialLatestSeenOrderId.value === null) {\n        initialLatestSeenOrderId.value = newPersistentLatestId; // 使用刚计算出的最新ID作为初始基准\n        console.log(`Set session initial latest ID (localStorage was empty) to: ${initialLatestSeenOrderId.value}`);\n    }\n\n    // --- 标记新订单 (基于会话初始 ID) ---\n    let processedOrders: OrderWithStatus[];\n    processedOrders = fetchedOrders.map(order => {\n        const numericOrderId = Number(order.id);\n        let isNew = false;\n        // 使用 initialLatestSeenOrderId.value 进行比较 (这个值现在来自 onMounted 或上面的新增逻辑)\n        if (initialLatestSeenOrderId.value !== null && !isNaN(numericOrderId)) {\n            isNew = numericOrderId > initialLatestSeenOrderId.value;\n        }\n        return { ...order, isNew: isNew };\n    });\n\n    orders.value = processedOrders\n    products.value = productsRes.data\n    services.value = servicesRes.data\n  } catch (error) {\n    console.error('获取数据失败:', error)\n    if (isInitialLoad) {\n        ElMessage.error('获取初始数据失败')\n    }\n  } finally {\n    if (isInitialLoad) {\n      loading.value = false\n    }\n  }\n}\n\nonMounted(() => {\n  // 从 localStorage 读取 LATEST_ORDER_ID_KEY\n  const storedIdString = localStorage.getItem(LATEST_ORDER_ID_KEY);\n  if (storedIdString) {\n    const storedId = parseInt(storedIdString, 10);\n    if (!isNaN(storedId)) {\n      latestSeenOrderId.value = storedId; // 初始化运行时的最新 ID\n      initialLatestSeenOrderId.value = storedId; // 同时初始化会话基准 ID\n    } else {\n        localStorage.removeItem(LATEST_ORDER_ID_KEY); // 清除无效值\n        // 保留 latestSeenOrderId 和 initialLatestSeenOrderId 为 null\n    }\n  }\n  // 如果 localStorage 为空或无效, latestSeenOrderId 和 initialLatestSeenOrderId 保持 null\n\n  console.log(`onMounted: Initial latestSeenOrderId from localStorage: ${latestSeenOrderId.value}`);\n  console.log(`onMounted: Initial initialLatestSeenOrderId set to: ${initialLatestSeenOrderId.value}`); // 添加日志确认\n\n  // 调用 fetchData 进行初始加载\n  fetchData(true); // 传入 true 表示是首次加载\n\n  // 清除可能存在的旧定时器（防御性编程）\n  if (pollingTimer) {\n    clearInterval(pollingTimer);\n  }\n  // 设置新的定时器\n  pollingTimer = setInterval(() => {\n      fetchData(false); // 后续调用表示非首次加载（轮询）\n  }, POLLING_INTERVAL);\n});\n\nconst getStatusType = (status: string) => {\n  switch (status) {\n    case 'PENDING':\n      return 'warning'\n    case 'ACTIVE': \n      return 'primary'\n    case 'USED':\n      return 'success'\n    case 'EXPIRED':\n      return 'info'\n    case 'REFUNDED':\n      return 'danger'\n    default:\n      return 'info'\n  }\n}\n\n\nconst getServiceName = (productId: number) => {\n  const product = products.value.find(p => p.id == productId)\n  const service = services.value.find(s => s.id == product?.serviceId)\n  return service?.name || '-'\n}\n\nconst getProductName = (productId: number) => {\n  const product = products.value.find(p => p.id == productId)\n    return product?.name || '-'\n}\n\nconst formatPrice = (price: number | string) => {\n  // 确保 price 是数字\n  const numPrice = typeof price === 'string' ? parseFloat(price) : price\n  \n  // 检查是否为有效数字\n  if (isNaN(numPrice)) {\n    return '¥0.00'\n  }\n  \n  return `¥ ${numPrice.toFixed(2)}`\n}\n\n\nconst handleDelete = async (row: Order) => {\n  try {\n    await ElMessageBox.confirm('确认删除该订单吗？', '提示', {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: 'warning'\n    })\n    \n    await deleteOrder(row.id)\n    ElMessage.success('删除成功')\n    await fetchData()  // 重新加载数据\n  } catch (error) {\n    if (error !== 'cancel') {  // 排除取消操作的情况\n      ElMessage.error('删除失败')\n      console.error('删除失败:', error)\n    }\n  }\n}\n\n// 按剩余数量排序\nconst sortByRemainingQuantity = (a: OrderWithStatus, b: OrderWithStatus) => {\n  const remainingA = a.quantity - a.usedQuantity\n  const remainingB = b.quantity - b.usedQuantity\n  return remainingA - remainingB\n}\n\n// 按价格排序\nconst sortByPrice = (a: OrderWithStatus, b: OrderWithStatus) => {\n  // 确保价格是数字\n  const priceA = typeof a.price === 'string' ? parseFloat(a.price) : a.price\n  const priceB = typeof b.price === 'string' ? parseFloat(b.price) : b.price\n  return priceA - priceB\n}\n\n// 按订单时间排序\nconst sortByDate = (a: OrderWithStatus, b: OrderWithStatus) => {\n  return dayjs(a.date).valueOf() - dayjs(b.date).valueOf()\n}\n\n// 按过期时间排序\nconst sortByExpiry = (a: OrderWithStatus, b: OrderWithStatus) => {\n  return dayjs(a.expiry).valueOf() - dayjs(b.expiry).valueOf()\n}\n\n// 修改筛选逻辑\nconst filteredOrders = computed(() => {\n  let result = [...orders.value]\n  \n  // 按手机号筛选\n  if (filterPhone.value.trim() !== '') {\n    const phoneFilter = filterPhone.value.trim().toLowerCase()\n    result = result.filter(order => {\n      return order.phone && order.phone.toString().toLowerCase().includes(phoneFilter)\n    })\n  }\n  \n  // 按状态筛选\n  if (filterStatus.value !== '') {\n    result = result.filter(order => {\n      return order.status === filterStatus.value\n    })\n  }\n  \n  // 按服务类型名称筛选\n  if (filterServiceId.value !== '') {\n    result = result.filter(order => {\n      return getServiceName(order.productId) === filterServiceId.value\n    })\n  }\n  \n  return result\n})\n\n// 新增函数\nconst tableRowClassName = ({ row }: { row: OrderWithStatus }) => {\n  if (row.isNew) {\n    return 'new-order-highlight';\n  }\n  return '';\n};\n\n// 新增 onBeforeUnmount\nonBeforeUnmount(() => {\n  if (pollingTimer) {\n    clearInterval(pollingTimer);\n    pollingTimer = null; // 清除引用\n  }\n});\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_option = _resolveComponent(\"el-option\")!\n  const _component_el_select = _resolveComponent(\"el-select\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n  const _component_el_card = _resolveComponent(\"el-card\")!\n  const _directive_loading = _resolveDirective(\"loading\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createVNode(_component_el_card, { class: \"box-card\" }, {\n      header: _withCtx(() => [\n        _createElementVNode(\"div\", _hoisted_2, [\n          _cache[3] || (_cache[3] = _createElementVNode(\"span\", null, \"订单管理\", -1)),\n          _createElementVNode(\"div\", _hoisted_3, [\n            _createElementVNode(\"div\", _hoisted_4, [\n              _createVNode(_component_el_input, {\n                modelValue: filterPhone.value,\n                \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((filterPhone).value = $event)),\n                placeholder: \"手机号\",\n                clearable: \"\",\n                class: \"filter-item phone-filter\"\n              }, {\n                prefix: _withCtx(() => [\n                  _createVNode(_component_el_icon, null, {\n                    default: _withCtx(() => [\n                      _createVNode(_unref(Search))\n                    ]),\n                    _: 1\n                  })\n                ]),\n                _: 1\n              }, 8, [\"modelValue\"]),\n              _createVNode(_component_el_select, {\n                modelValue: filterStatus.value,\n                \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((filterStatus).value = $event)),\n                placeholder: \"全部\",\n                clearable: \"\",\n                class: \"filter-item status-filter\"\n              }, {\n                default: _withCtx(() => [\n                  _createVNode(_component_el_option, {\n                    label: \"全部\",\n                    value: \"\"\n                  }),\n                  _createVNode(_component_el_option, {\n                    label: \"待支付\",\n                    value: \"PENDING\"\n                  }),\n                  _createVNode(_component_el_option, {\n                    label: \"使用中\",\n                    value: \"ACTIVE\"\n                  }),\n                  _createVNode(_component_el_option, {\n                    label: \"已用完\",\n                    value: \"USED\"\n                  }),\n                  _createVNode(_component_el_option, {\n                    label: \"已过期\",\n                    value: \"EXPIRED\"\n                  }),\n                  _createVNode(_component_el_option, {\n                    label: \"已退款\",\n                    value: \"REFUNDED\"\n                  })\n                ]),\n                _: 1\n              }, 8, [\"modelValue\"]),\n              _createVNode(_component_el_select, {\n                modelValue: filterServiceId.value,\n                \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((filterServiceId).value = $event)),\n                placeholder: \"全部\",\n                clearable: \"\",\n                class: \"filter-item service-filter\"\n              }, {\n                default: _withCtx(() => [\n                  _createVNode(_component_el_option, {\n                    label: \"全部\",\n                    value: \"\"\n                  }),\n                  (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(services.value, (service) => {\n                    return (_openBlock(), _createBlock(_component_el_option, {\n                      key: service.id,\n                      label: service.name,\n                      value: service.name\n                    }, null, 8, [\"label\", \"value\"]))\n                  }), 128))\n                ]),\n                _: 1\n              }, 8, [\"modelValue\"])\n            ])\n          ])\n        ])\n      ]),\n      default: _withCtx(() => [\n        _withDirectives((_openBlock(), _createBlock(_component_el_table, {\n          data: filteredOrders.value,\n          style: {\"width\":\"100%\"},\n          \"row-class-name\": tableRowClassName\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_component_el_table_column, {\n              prop: \"id\",\n              label: \"订单号\",\n              width: \"100\"\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"状态\",\n              width: \"100\"\n            }, {\n              default: _withCtx(({ row }) => [\n                _createVNode(_component_el_tag, {\n                  type: getStatusType(row.status)\n                }, {\n                  default: _withCtx(() => [\n                    _createTextVNode(_toDisplayString(row.status === 'PENDING' ? '待支付' :\n                row.status === 'ACTIVE' ? '使用中' :\n                row.status === 'USED' ? '已用完' :\n                row.status === 'EXPIRED' ? '已过期' :\n                row.status === 'REFUNDED' ? '已退款' : '未知'), 1)\n                  ]),\n                  _: 2\n                }, 1032, [\"type\"])\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"服务类型\",\n              width: \"120\"\n            }, {\n              default: _withCtx(({ row }) => [\n                _createTextVNode(_toDisplayString(getServiceName(row.productId)), 1)\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              prop: \"productId\",\n              label: \"产品号\",\n              width: \"100\"\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"产品名称\",\n              \"min-width\": \"180\"\n            }, {\n              default: _withCtx(({ row }) => [\n                _createTextVNode(_toDisplayString(getProductName(row.productId)), 1)\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"数量\",\n              width: \"120\",\n              sortable: \"\",\n              \"sort-method\": sortByRemainingQuantity\n            }, {\n              default: _withCtx(({ row }) => [\n                _createTextVNode(_toDisplayString(row.quantity - row.usedQuantity) + \"/\" + _toDisplayString(row.quantity), 1)\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"总金额\",\n              width: \"120\",\n              sortable: \"\",\n              \"sort-method\": sortByPrice\n            }, {\n              default: _withCtx(({ row }) => [\n                _createTextVNode(_toDisplayString(formatPrice(row.price)), 1)\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              prop: \"phone\",\n              label: \"买家\",\n              width: \"120\"\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"订单时间\",\n              width: \"180\",\n              sortable: \"\",\n              \"sort-method\": sortByDate\n            }, {\n              default: _withCtx(({ row }) => [\n                _createTextVNode(_toDisplayString(_unref(dayjs)(row.date).format('YYYY-MM-DD HH:mm:ss')), 1)\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"过期时间\",\n              width: \"180\",\n              sortable: \"\",\n              \"sort-method\": sortByExpiry\n            }, {\n              default: _withCtx(({ row }) => [\n                _createTextVNode(_toDisplayString(_unref(dayjs)(row.expiry).format('YYYY-MM-DD HH:mm:ss')), 1)\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_table_column, {\n              label: \"操作\",\n              width: \"150\",\n              fixed: \"right\"\n            }, {\n              default: _withCtx((scope) => [\n                _createVNode(_component_el_button, {\n                  size: \"small\",\n                  type: \"danger\",\n                  onClick: ($event: any) => (handleDelete(scope.row))\n                }, {\n                  default: _withCtx(() => _cache[4] || (_cache[4] = [\n                    _createTextVNode(\"删除\")\n                  ])),\n                  _: 2\n                }, 1032, [\"onClick\"])\n              ]),\n              _: 1\n            })\n          ]),\n          _: 1\n        }, 8, [\"data\"])), [\n          [_directive_loading, loading.value]\n        ])\n      ]),\n      _: 1\n    })\n  ]))\n}\n}\n\n})", "<template>\n  <div class=\"order-view\">\n    <el-card class=\"box-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <span>订单管理</span>\n          <div class=\"header-operations\">\n            <div class=\"filter-group\">\n              <el-input\n                v-model=\"filterPhone\"\n                placeholder=\"手机号\"\n                clearable\n                class=\"filter-item phone-filter\"\n              >\n                <template #prefix>\n                  <el-icon><Search /></el-icon>\n                </template>\n              </el-input>\n\n              <el-select\n                v-model=\"filterStatus\"\n                placeholder=\"全部\"\n                clearable\n                class=\"filter-item status-filter\"\n              >\n                <el-option label=\"全部\" value=\"\" />\n                <el-option label=\"待支付\" value=\"PENDING\" />\n                <el-option label=\"使用中\" value=\"ACTIVE\" />\n                <el-option label=\"已用完\" value=\"USED\" />\n                <el-option label=\"已过期\" value=\"EXPIRED\" />\n                <el-option label=\"已退款\" value=\"REFUNDED\" />\n              </el-select>\n\n              <el-select\n                v-model=\"filterServiceId\"\n                placeholder=\"全部\"\n                clearable\n                class=\"filter-item service-filter\"\n              >\n                <el-option label=\"全部\" value=\"\" />\n                <el-option\n                  v-for=\"service in services\"\n                  :key=\"service.id\"\n                  :label=\"service.name\"\n                  :value=\"service.name\"\n                />\n              </el-select>\n            </div>\n          </div>\n        </div>\n      </template>\n      <el-table v-loading=\"loading\" :data=\"filteredOrders\" style=\"width: 100%\" :row-class-name=\"tableRowClassName\">\n        <el-table-column prop=\"id\" label=\"订单号\" width=\"100\" />\n        <el-table-column label=\"状态\" width=\"100\">\n          <template #default=\"{ row }\">\n            <el-tag :type=\"getStatusType(row.status)\">\n              {{ \n                row.status === 'PENDING' ? '待支付' :\n                row.status === 'ACTIVE' ? '使用中' :\n                row.status === 'USED' ? '已用完' :\n                row.status === 'EXPIRED' ? '已过期' :\n                row.status === 'REFUNDED' ? '已退款' : '未知'\n              }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"服务类型\" width=\"120\">\n          <template #default=\"{ row }\">\n            {{ getServiceName(row.productId) }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"productId\" label=\"产品号\" width=\"100\" />\n        <el-table-column label=\"产品名称\" min-width=\"180\">\n          <template #default=\"{ row }\">\n            {{ getProductName(row.productId) }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"数量\" width=\"120\" sortable :sort-method=\"sortByRemainingQuantity\">\n          <template #default=\"{ row }\">\n            {{ row.quantity - row.usedQuantity }}/{{ row.quantity }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"总金额\" width=\"120\" sortable :sort-method=\"sortByPrice\">\n          <template #default=\"{ row }\">\n            {{ formatPrice(row.price) }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"phone\" label=\"买家\" width=\"120\" />\n        <el-table-column label=\"订单时间\" width=\"180\" sortable :sort-method=\"sortByDate\">\n          <template #default=\"{ row }\">\n            {{ dayjs(row.date).format('YYYY-MM-DD HH:mm:ss') }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"过期时间\" width=\"180\" sortable :sort-method=\"sortByExpiry\">\n          <template #default=\"{ row }\">\n            {{ dayjs(row.expiry).format('YYYY-MM-DD HH:mm:ss') }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n          <template #default=\"scope\">\n            <el-button\n              size=\"small\"\n              type=\"danger\"\n              @click=\"handleDelete(scope.row)\"\n            >删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-card>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted, computed, onBeforeUnmount } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { getOrders, deleteOrder } from '@/api/order'\nimport { getProducts, getServices } from '@/api/product'\nimport type { Order } from '@/api/order'\nimport type { Product, Service } from '@/api/product'\nimport dayjs from 'dayjs'\nimport { Search } from '@element-plus/icons-vue'\n\n// 新增常量\nconst LATEST_ORDER_ID_KEY = 'latestOrderId';\nconst POLLING_INTERVAL = 60000; // 60 秒\n\nconst loading = ref(false)\nconst orders = ref<Order[]>([])\nconst products = ref<Product[]>([])\nconst services = ref<Service[]>([])\n\n// 新增状态变量\nconst latestSeenOrderId = ref<number | null>(null);\nconst initialLatestSeenOrderId = ref<number | null>(null); // 新增：存储会话初始ID\nlet pollingTimer: number | null = null;\n\n// 筛选变量的默认值\nconst filterStatus = ref('')  // 空字符串对应\"全部\"\nconst filterServiceId = ref('')  // 空字符串对应\"全部\"\nconst filterPhone = ref('')  // 新增：手机号过滤\n\n// 扩展 Order 类型以包含 isNew\ninterface OrderWithStatus extends Order {\n  isNew?: boolean;\n}\n\nconst fetchData = async (isInitialLoad = false) => {\n  if (isInitialLoad) {\n    loading.value = true\n  }\n  try {\n    const [ordersRes, productsRes, servicesRes] = await Promise.all([\n      getOrders(),\n      getProducts(),\n      getServices()\n    ])\n\n    const fetchedOrders: Order[] = ordersRes.data || [];\n    let currentMaxId = 0;\n    if (fetchedOrders.length > 0) {\n      currentMaxId = Math.max(...fetchedOrders.map(o => Number(o.id)).filter(id => !isNaN(id)));\n    }\n\n    // --- 更新持久化最新 ID (latestSeenOrderId) ---\n    const persistentLatestId = latestSeenOrderId.value;\n    let newPersistentLatestId = persistentLatestId;\n\n    if (persistentLatestId === null) { // 初始化\n        newPersistentLatestId = currentMaxId;\n    } else if (currentMaxId > persistentLatestId) { // 更新\n        newPersistentLatestId = currentMaxId;\n    }\n\n    // 如果值改变或被初始化，则更新 ref 和 localStorage\n    if (newPersistentLatestId !== persistentLatestId) {\n        latestSeenOrderId.value = newPersistentLatestId;\n        localStorage.setItem(LATEST_ORDER_ID_KEY, String(newPersistentLatestId));\n        console.log(`Updated persistent latest ID to: ${newPersistentLatestId}`);\n    }\n\n    // --- (新增逻辑) 处理首次加载且localStorage为空的情况，设置会话初始ID ---\n    // 只有在 initialLatestSeenOrderId 尚未被 onMounted 设置（即localStorage为空或无效）\n    // 且这是首次加载时，才在这里设置它\n    if (isInitialLoad && initialLatestSeenOrderId.value === null) {\n        initialLatestSeenOrderId.value = newPersistentLatestId; // 使用刚计算出的最新ID作为初始基准\n        console.log(`Set session initial latest ID (localStorage was empty) to: ${initialLatestSeenOrderId.value}`);\n    }\n\n    // --- 标记新订单 (基于会话初始 ID) ---\n    let processedOrders: OrderWithStatus[];\n    processedOrders = fetchedOrders.map(order => {\n        const numericOrderId = Number(order.id);\n        let isNew = false;\n        // 使用 initialLatestSeenOrderId.value 进行比较 (这个值现在来自 onMounted 或上面的新增逻辑)\n        if (initialLatestSeenOrderId.value !== null && !isNaN(numericOrderId)) {\n            isNew = numericOrderId > initialLatestSeenOrderId.value;\n        }\n        return { ...order, isNew: isNew };\n    });\n\n    orders.value = processedOrders\n    products.value = productsRes.data\n    services.value = servicesRes.data\n  } catch (error) {\n    console.error('获取数据失败:', error)\n    if (isInitialLoad) {\n        ElMessage.error('获取初始数据失败')\n    }\n  } finally {\n    if (isInitialLoad) {\n      loading.value = false\n    }\n  }\n}\n\nonMounted(() => {\n  // 从 localStorage 读取 LATEST_ORDER_ID_KEY\n  const storedIdString = localStorage.getItem(LATEST_ORDER_ID_KEY);\n  if (storedIdString) {\n    const storedId = parseInt(storedIdString, 10);\n    if (!isNaN(storedId)) {\n      latestSeenOrderId.value = storedId; // 初始化运行时的最新 ID\n      initialLatestSeenOrderId.value = storedId; // 同时初始化会话基准 ID\n    } else {\n        localStorage.removeItem(LATEST_ORDER_ID_KEY); // 清除无效值\n        // 保留 latestSeenOrderId 和 initialLatestSeenOrderId 为 null\n    }\n  }\n  // 如果 localStorage 为空或无效, latestSeenOrderId 和 initialLatestSeenOrderId 保持 null\n\n  console.log(`onMounted: Initial latestSeenOrderId from localStorage: ${latestSeenOrderId.value}`);\n  console.log(`onMounted: Initial initialLatestSeenOrderId set to: ${initialLatestSeenOrderId.value}`); // 添加日志确认\n\n  // 调用 fetchData 进行初始加载\n  fetchData(true); // 传入 true 表示是首次加载\n\n  // 清除可能存在的旧定时器（防御性编程）\n  if (pollingTimer) {\n    clearInterval(pollingTimer);\n  }\n  // 设置新的定时器\n  pollingTimer = setInterval(() => {\n      fetchData(false); // 后续调用表示非首次加载（轮询）\n  }, POLLING_INTERVAL);\n});\n\nconst getStatusType = (status: string) => {\n  switch (status) {\n    case 'PENDING':\n      return 'warning'\n    case 'ACTIVE': \n      return 'primary'\n    case 'USED':\n      return 'success'\n    case 'EXPIRED':\n      return 'info'\n    case 'REFUNDED':\n      return 'danger'\n    default:\n      return 'info'\n  }\n}\n\n\nconst getServiceName = (productId: number) => {\n  const product = products.value.find(p => p.id == productId)\n  const service = services.value.find(s => s.id == product?.serviceId)\n  return service?.name || '-'\n}\n\nconst getProductName = (productId: number) => {\n  const product = products.value.find(p => p.id == productId)\n    return product?.name || '-'\n}\n\nconst formatPrice = (price: number | string) => {\n  // 确保 price 是数字\n  const numPrice = typeof price === 'string' ? parseFloat(price) : price\n  \n  // 检查是否为有效数字\n  if (isNaN(numPrice)) {\n    return '¥0.00'\n  }\n  \n  return `¥ ${numPrice.toFixed(2)}`\n}\n\n\nconst handleDelete = async (row: Order) => {\n  try {\n    await ElMessageBox.confirm('确认删除该订单吗？', '提示', {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: 'warning'\n    })\n    \n    await deleteOrder(row.id)\n    ElMessage.success('删除成功')\n    await fetchData()  // 重新加载数据\n  } catch (error) {\n    if (error !== 'cancel') {  // 排除取消操作的情况\n      ElMessage.error('删除失败')\n      console.error('删除失败:', error)\n    }\n  }\n}\n\n// 按剩余数量排序\nconst sortByRemainingQuantity = (a: OrderWithStatus, b: OrderWithStatus) => {\n  const remainingA = a.quantity - a.usedQuantity\n  const remainingB = b.quantity - b.usedQuantity\n  return remainingA - remainingB\n}\n\n// 按价格排序\nconst sortByPrice = (a: OrderWithStatus, b: OrderWithStatus) => {\n  // 确保价格是数字\n  const priceA = typeof a.price === 'string' ? parseFloat(a.price) : a.price\n  const priceB = typeof b.price === 'string' ? parseFloat(b.price) : b.price\n  return priceA - priceB\n}\n\n// 按订单时间排序\nconst sortByDate = (a: OrderWithStatus, b: OrderWithStatus) => {\n  return dayjs(a.date).valueOf() - dayjs(b.date).valueOf()\n}\n\n// 按过期时间排序\nconst sortByExpiry = (a: OrderWithStatus, b: OrderWithStatus) => {\n  return dayjs(a.expiry).valueOf() - dayjs(b.expiry).valueOf()\n}\n\n// 修改筛选逻辑\nconst filteredOrders = computed(() => {\n  let result = [...orders.value]\n  \n  // 按手机号筛选\n  if (filterPhone.value.trim() !== '') {\n    const phoneFilter = filterPhone.value.trim().toLowerCase()\n    result = result.filter(order => {\n      return order.phone && order.phone.toString().toLowerCase().includes(phoneFilter)\n    })\n  }\n  \n  // 按状态筛选\n  if (filterStatus.value !== '') {\n    result = result.filter(order => {\n      return order.status === filterStatus.value\n    })\n  }\n  \n  // 按服务类型名称筛选\n  if (filterServiceId.value !== '') {\n    result = result.filter(order => {\n      return getServiceName(order.productId) === filterServiceId.value\n    })\n  }\n  \n  return result\n})\n\n// 新增函数\nconst tableRowClassName = ({ row }: { row: OrderWithStatus }) => {\n  if (row.isNew) {\n    return 'new-order-highlight';\n  }\n  return '';\n};\n\n// 新增 onBeforeUnmount\nonBeforeUnmount(() => {\n  if (pollingTimer) {\n    clearInterval(pollingTimer);\n    pollingTimer = null; // 清除引用\n  }\n});\n</script>\n\n<style scoped>\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-operations {\n  display: flex;\n  align-items: center;\n  gap: 32px;\n}\n\n.filter-group {\n  display: flex;\n  gap: 16px;\n}\n\n.filter-item {\n  width: 120px;\n}\n\n.phone-filter {\n  width: 150px;\n}\n\n.status-filter {\n  width: 100px;\n}\n\n.service-filter {\n  width: 110px;\n}\n\n/* 新增高亮样式 */\n/* 注意：直接在 scoped style 中修改 el-table 的深层样式可能需要 :deep() */\n:deep(.el-table .new-order-highlight td) {\n  background-color: #e1f3d8 !important; /* 一个淡绿色背景 */\n  transition: background-color 0.5s ease-in-out;\n}\n\n/* 可选：鼠标悬停时保持高亮 */\n:deep(.el-table .new-order-highlight:hover td) {\n  background-color: #d1eec3 !important;\n}\n</style> ", "import script from \"./OrderView.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./OrderView.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./OrderView.vue?vue&type=style&index=0&id=0d2d4a10&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/.pnpm/vue-loader@17.4.2_@vue+compiler-sfc@3.5.13_vue@3.5.13_typescript@5.8.3__webpack@5.98.0/node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-0d2d4a10\"]])\n\nexport default __exports__", "import request from '@/utils/request'\r\n\r\n// 后端返回的原始数据类型\r\ninterface OrderResponse {\r\n  id: string\r\n  productId: string\r\n  quantity: string\r\n  usedQuantity: string\r\n  price: string\r\n  phone: string\r\n  date: string\r\n  expiry: string\r\n  status: string\r\n}\r\n\r\n// 转换后的数据类型\r\nexport interface Order {\r\n  id: number\r\n  productId: number\r\n  quantity: number\r\n  usedQuantity: number\r\n  price: number\r\n  phone: string \r\n  date: string\r\n  expiry: string\r\n  status: string\r\n}\r\n\r\nexport const getOrders = async () => {\r\n  const response = await request<OrderResponse[]>({\r\n    url: '/queryAllMPOrders',\r\n    method: 'get'\r\n  })\r\n  \r\n  // 转换数据\r\n  return {\r\n    ...response,\r\n    data: response.data.map(order => ({\r\n      id: parseInt(order.id),\r\n      productId: parseInt(order.productId),\r\n      quantity: parseInt(order.quantity),\r\n      usedQuantity: parseInt(order.usedQuantity),\r\n      price: parseFloat(order.price),\r\n      phone: order.phone,\r\n      date: order.date,\r\n      expiry: order.expiry,\r\n      status: order.status\r\n    }))\r\n  }\r\n}\r\n\r\nexport const modifyOrder = (orderVO: Order) => {\r\n  return request({\r\n    url: '/modifyMPOrder',\r\n    method: 'post',\r\n    data: orderVO\r\n  })\r\n}\r\n\r\nexport const deleteOrder = (mpOrderId: number) => {\r\n  return request({\r\n    url: '/deleteMPOrder',\r\n    method: 'post',\r\n    params: { mpOrderId }\r\n  })\r\n}\r\n\r\nexport const deleteOrders = (mpOrderIds: number[]) => {\r\n  return request({\r\n    url: '/deleteMPOrders',\r\n    method: 'post',\r\n    params: { mpOrderIds }\r\n  })\r\n} ", "import request from '@/utils/request'\n\n// 产品类型定义\n\nexport interface Service {\n  id: number\n  name: string\n}\n\nexport interface Category {\n  id: number\n  name: string\n  serviceId: number\n}\n\nexport interface Brand {\n  id: number\n  name: string\n  categoryId: number\n}\n\nexport interface Product {\n  id: number\n  name: string\n  price: number\n  quantity: number\n  factoryPrice: number\n  serviceId: number\n  categoryId: number\n  brandId: number\n  isService: boolean\n}\n\nexport interface MPProduct {\n  id: number\n  productId: number\n  tags: string\n  quantity: number\n  price: number\n  expiry: string\n  validDays: number\n}\n\nexport interface ProductImage {\n  productId: number\n  fileName: string\n}\n\n// 创建产品参数\nexport interface MPProductCreateParams {\n  productId: number\n  tags: string\n  quantity: number\n  price: number\n  expiry: string\n  validDays: number\n}\n\n// 更新产品参数\nexport interface MPProductUpdateParams extends MPProductCreateParams {\n  id: number\n}\n\n// API 方法\n\n\n\nexport const getServices = () => {\n  return request({\n    url: '/queryServices',\n    method: 'get'\n  })\n} \n\nexport const getCategories = () => {\n  return request({\n    url: '/queryCategories',\n    method: 'get'\n  })\n}\n\nexport const getBrands = () => {\n  return request({\n    url: '/queryBrands',\n    method: 'get'\n  })\n}\n\nexport const getProducts = () => {\n  return request({\n    url: '/queryProducts',\n    method: 'get'\n  })\n}\n\nexport const getMPProducts = () => {\n  return request({\n    url: '/queryMPProducts',\n    method: 'get'\n  })\n}\n\nexport const createMPProduct = (productVO: MPProductCreateParams) => {\n  return request({\n    url: '/createMPProduct',\n    method: 'post',\n    data: productVO\n  })\n}\n\nexport const modifyMPProduct = (mpProductVO: MPProductUpdateParams) => {\n  return request({\n    url: '/modifyMPProduct',\n    method: 'post',\n    data: mpProductVO\n  })\n}\n\nexport const deleteMPProduct = (mpProductId: number) => {\n  return request({\n    url: '/deleteMPProduct',\n    method: 'post',\n    params: { mpProductId }\n  })\n}\n\nexport const deleteMPProducts = (mpProductIds: number[]) => {\n  return request({\n    url: '/deleteMPProducts',\n    method: 'post',\n    params: { mpProductIds }\n  })\n}\n\nexport const setProductImage = (productId: number, image: File) => {\n  const formData = new FormData()\n  formData.append('productId', productId.toString())\n  formData.append('image', image)\n  return request({\n    url: '/setProductImage', \n    method: 'post',\n    data: formData,\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  })\n}\n\nexport const getProductImages = () => {\n  return request({\n    url: '/queryProductImages',\n    method: 'get'\n  })\n}\n"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "LATEST_ORDER_ID_KEY", "POLLING_INTERVAL", "_defineComponent", "__name", "setup", "__props", "loading", "ref", "orders", "products", "services", "latestSeenOrderId", "initialLatestSeenOrderId", "pollingTimer", "filterStatus", "filterServiceId", "filterPhone", "fetchData", "async", "isInitialLoad", "value", "ordersRes", "productsRes", "servicesRes", "Promise", "all", "getOrders", "getProducts", "getServices", "fetchedOrders", "data", "currentMaxId", "length", "Math", "max", "map", "o", "Number", "id", "filter", "isNaN", "persistentLatestId", "processedOrders", "newPersistentLatestId", "localStorage", "setItem", "String", "console", "log", "order", "numericOrderId", "isNew", "error", "ElMessage", "onMounted", "storedIdString", "getItem", "storedId", "parseInt", "removeItem", "clearInterval", "setInterval", "getStatusType", "status", "getServiceName", "productId", "product", "find", "p", "service", "s", "serviceId", "name", "getProductName", "formatPrice", "price", "numPrice", "parseFloat", "toFixed", "handleDelete", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "type", "deleteOrder", "row", "success", "sortByRemainingQuantity", "a", "b", "remainingA", "quantity", "usedQuantity", "remainingB", "sortByPrice", "priceA", "priceB", "sortByDate", "dayjs", "date", "valueOf", "sortByExpiry", "expiry", "filteredOrders", "computed", "result", "trim", "phoneFilter", "toLowerCase", "phone", "toString", "includes", "tableRowClassName", "onBeforeUnmount", "_ctx", "_cache", "_component_el_icon", "_resolveComponent", "_component_el_input", "_component_el_option", "_component_el_select", "_component_el_table_column", "_component_el_tag", "_component_el_button", "_component_el_table", "_component_el_card", "_directive_loading", "_resolveDirective", "_openBlock", "_createElementBlock", "_createVNode", "header", "_withCtx", "_createElementVNode", "modelValue", "$event", "placeholder", "clearable", "prefix", "default", "_unref", "Search", "_", "label", "_Fragment", "_renderList", "_createBlock", "key", "_withDirectives", "style", "prop", "width", "_createTextVNode", "_toDisplayString", "sortable", "format", "fixed", "scope", "size", "onClick", "__exports__", "response", "request", "url", "method", "mpOrderId", "params", "getCategories", "getBrands", "getMPProducts", "createMPProduct", "productVO", "modifyMPProduct", "mpProductVO", "deleteMPProduct", "mpProductId", "setProductImage", "image", "formData", "FormData", "append", "headers", "getProductImages"], "sourceRoot": ""}