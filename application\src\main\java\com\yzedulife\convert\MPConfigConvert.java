package com.yzedulife.convert;

import com.yzedulife.service.dto.MPConfigDTO;
import com.yzedulife.response.MPConfigResponse;
import com.yzedulife.vo.MPConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MPConfigConvert{
    MPConfigConvert INSTANCE = Mappers.getMapper(MPConfigConvert.class);

    MPConfigDTO vo2dto(MPConfigVO mpConfigVO);

    MPConfigResponse dto2res(MPConfigDTO mpConfigDTO);
}
