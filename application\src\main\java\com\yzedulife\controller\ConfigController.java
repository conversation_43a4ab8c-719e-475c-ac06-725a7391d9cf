package com.yzedulife.controller;

import com.yzedulife.annotation.Token;
import com.yzedulife.service.dto.MPConfigDTO;
import com.yzedulife.response.Response;
import com.yzedulife.service.service.MPConfigService;
import com.yzedulife.vo.MPConfigVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

@RestController
@Tag(name = "配置 接口")
public class ConfigController {
    @Autowired
    private MPConfigService mpConfigService;

    @Token
    @Operation(summary = "修改配置")
    @PostMapping("/modifyConfig")
    public Response modifyConfig(@RequestBody MPConfigVO mpConfigVO) {
        mpConfigService.modify(mpConfigVO.toDTO());
        return Response.success().msg("修改配置成功");
    }

    @Operation(summary = "查询配置")
    @GetMapping("/queryConfigs")
    public Response queryConfigs() {
        List<MPConfigDTO> mpConfigDTOs = mpConfigService.queryBatch();
        return Response.success().data(mpConfigDTOs);
    }

    @Token
    @Operation(summary = "上传门店照片")
    @PostMapping("/uploadStorePhoto")
    public Response uploadStorePhoto(@RequestParam MultipartFile photo) {
        try {
            String uploadDir = Paths.get("covers").toAbsolutePath().toString();
            File directory = new File(uploadDir);
            if (!directory.exists()) directory.mkdir();

            String extension = photo.getOriginalFilename().substring(photo.getOriginalFilename().lastIndexOf("."));
            String fileName = "storePhoto" + extension;
            String filePath = uploadDir + File.separator + fileName;

            File dest = new File(filePath);
            photo.transferTo(dest);

            return Response.success().data(fileName);
        } catch (IOException e) {
            return Response.error().msg("照片上传失败");
        }
    }

    @Operation(summary = "获取门店照片")
    @GetMapping("/storePhoto")
    public ResponseEntity<Resource> getStorePhoto() {
        try {
            String uploadDir = "covers";
            // Find the storePhoto file with any extension
            File directory = new File(uploadDir);
            File storePhotoFile = null;
            
            if (directory.exists() && directory.isDirectory()) {
                File[] files = directory.listFiles();
                if (files != null) {
                    for (File file : files) {
                        if (file.isFile() && file.getName().replace("covers\\", "").startsWith("storePhoto")) {
                            storePhotoFile = file;
                            break;
                        }
                    }
                }
            }
            
            if (storePhotoFile == null) {
                return ResponseEntity.notFound().build();
            }

            Path path = storePhotoFile.toPath();
            Resource resource = new FileSystemResource(path);
            String contentType = Files.probeContentType(path);

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .body(resource);
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}