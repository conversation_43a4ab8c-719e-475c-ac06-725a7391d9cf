"use strict";(self["webpackChunkadmin_web"]=self["webpackChunkadmin_web"]||[]).push([[28],{3028:function(e,t,a){a.r(t),a.d(t,{default:function(){return F}});a(7335),a(9710),a(1468),a(7898);var l=a(4922),r=a(1402),u=a(2959),o=a(7415),n=a(6503),d=a(5312),s=a(5663),i=a(6211),c=a.n(i),p=a(3013);const v={class:"order-view"},m={class:"card-header"},f={class:"header-operations"},b={class:"filter-group"},h="latestOrderId",g=6e4;var w=(0,l.pM)({__name:"OrderView",setup(e){const t=(0,r.KR)(!1),a=(0,r.KR)([]),i=(0,r.KR)([]),w=(0,r.KR)([]),y=(0,r.KR)(null),I=(0,r.KR)(null);let F=null;const k=(0,r.KR)(""),_=(0,r.KR)(""),E=(0,r.KR)(""),D=async(e=!1)=>{e&&(t.value=!0);try{const[t,l,r]=await Promise.all([(0,d.WF)(),(0,s.d$)(),(0,s.Z1)()]),u=t.data||[];let o=0;u.length>0&&(o=Math.max(...u.map((e=>Number(e.id))).filter((e=>!isNaN(e)))));const n=y.value;let c,p=n;(null===n||o>n)&&(p=o),p!==n&&(y.value=p,localStorage.setItem(h,String(p)),console.log(`Updated persistent latest ID to: ${p}`)),e&&null===I.value&&(I.value=p,console.log(`Set session initial latest ID (localStorage was empty) to: ${I.value}`)),c=u.map((e=>{const t=Number(e.id);let a=!1;return null===I.value||isNaN(t)||(a=t>I.value),{...e,isNew:a}})),a.value=c,i.value=l.data,w.value=r.data}catch(l){console.error("获取数据失败:",l),e&&o.nk.error("获取初始数据失败")}finally{e&&(t.value=!1)}};(0,l.sV)((()=>{const e=localStorage.getItem(h);if(e){const t=parseInt(e,10);isNaN(t)?localStorage.removeItem(h):(y.value=t,I.value=t)}console.log(`onMounted: Initial latestSeenOrderId from localStorage: ${y.value}`),console.log(`onMounted: Initial initialLatestSeenOrderId set to: ${I.value}`),D(!0),F&&clearInterval(F),F=setInterval((()=>{D(!1)}),g)}));const N=e=>{switch(e){case"PENDING":return"warning";case"ACTIVE":return"primary";case"USED":return"success";case"EXPIRED":return"info";case"REFUNDED":return"danger";default:return"info"}},P=e=>{const t=i.value.find((t=>t.id==e)),a=w.value.find((e=>e.id==t?.serviceId));return a?.name||"-"},A=e=>{const t=i.value.find((t=>t.id==e));return t?.name||"-"},R=e=>{const t="string"===typeof e?parseFloat(e):e;return isNaN(t)?"¥0.00":`¥ ${t.toFixed(2)}`},S=async e=>{try{await n.s.confirm("确认删除该订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await(0,d.o8)(e.id),o.nk.success("删除成功"),await D()}catch(t){"cancel"!==t&&(o.nk.error("删除失败"),console.error("删除失败:",t))}},M=(e,t)=>{const a=e.quantity-e.usedQuantity,l=t.quantity-t.usedQuantity;return a-l},V=(e,t)=>{const a="string"===typeof e.price?parseFloat(e.price):e.price,l="string"===typeof t.price?parseFloat(t.price):t.price;return a-l},W=(e,t)=>c()(e.date).valueOf()-c()(t.date).valueOf(),q=(e,t)=>c()(e.expiry).valueOf()-c()(t.expiry).valueOf(),x=(0,l.EW)((()=>{let e=[...a.value];if(""!==E.value.trim()){const t=E.value.trim().toLowerCase();e=e.filter((e=>e.phone&&e.phone.toString().toLowerCase().includes(t)))}return""!==k.value&&(e=e.filter((e=>e.status===k.value))),""!==_.value&&(e=e.filter((e=>P(e.productId)===_.value))),e})),C=({row:e})=>e.isNew?"new-order-highlight":"";return(0,l.xo)((()=>{F&&(clearInterval(F),F=null)})),(e,a)=>{const o=(0,l.g2)("el-icon"),n=(0,l.g2)("el-input"),d=(0,l.g2)("el-option"),s=(0,l.g2)("el-select"),i=(0,l.g2)("el-table-column"),h=(0,l.g2)("el-tag"),g=(0,l.g2)("el-button"),y=(0,l.g2)("el-table"),I=(0,l.g2)("el-card"),F=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",v,[(0,l.bF)(I,{class:"box-card"},{header:(0,l.k6)((()=>[(0,l.Lk)("div",m,[a[3]||(a[3]=(0,l.Lk)("span",null,"订单管理",-1)),(0,l.Lk)("div",f,[(0,l.Lk)("div",b,[(0,l.bF)(n,{modelValue:E.value,"onUpdate:modelValue":a[0]||(a[0]=e=>E.value=e),placeholder:"手机号",clearable:"",class:"filter-item phone-filter"},{prefix:(0,l.k6)((()=>[(0,l.bF)(o,null,{default:(0,l.k6)((()=>[(0,l.bF)((0,r.R1)(p.Search))])),_:1})])),_:1},8,["modelValue"]),(0,l.bF)(s,{modelValue:k.value,"onUpdate:modelValue":a[1]||(a[1]=e=>k.value=e),placeholder:"全部",clearable:"",class:"filter-item status-filter"},{default:(0,l.k6)((()=>[(0,l.bF)(d,{label:"全部",value:""}),(0,l.bF)(d,{label:"待支付",value:"PENDING"}),(0,l.bF)(d,{label:"使用中",value:"ACTIVE"}),(0,l.bF)(d,{label:"已用完",value:"USED"}),(0,l.bF)(d,{label:"已过期",value:"EXPIRED"}),(0,l.bF)(d,{label:"已退款",value:"REFUNDED"})])),_:1},8,["modelValue"]),(0,l.bF)(s,{modelValue:_.value,"onUpdate:modelValue":a[2]||(a[2]=e=>_.value=e),placeholder:"全部",clearable:"",class:"filter-item service-filter"},{default:(0,l.k6)((()=>[(0,l.bF)(d,{label:"全部",value:""}),((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(w.value,(e=>((0,l.uX)(),(0,l.Wv)(d,{key:e.id,label:e.name,value:e.name},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])])])])),default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(y,{data:x.value,style:{width:"100%"},"row-class-name":C},{default:(0,l.k6)((()=>[(0,l.bF)(i,{prop:"id",label:"订单号",width:"100"}),(0,l.bF)(i,{label:"状态",width:"100"},{default:(0,l.k6)((({row:e})=>[(0,l.bF)(h,{type:N(e.status)},{default:(0,l.k6)((()=>[(0,l.eW)((0,u.v_)("PENDING"===e.status?"待支付":"ACTIVE"===e.status?"使用中":"USED"===e.status?"已用完":"EXPIRED"===e.status?"已过期":"REFUNDED"===e.status?"已退款":"未知"),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(i,{label:"服务类型",width:"120"},{default:(0,l.k6)((({row:e})=>[(0,l.eW)((0,u.v_)(P(e.productId)),1)])),_:1}),(0,l.bF)(i,{prop:"productId",label:"产品号",width:"100"}),(0,l.bF)(i,{label:"产品名称","min-width":"180"},{default:(0,l.k6)((({row:e})=>[(0,l.eW)((0,u.v_)(A(e.productId)),1)])),_:1}),(0,l.bF)(i,{label:"数量",width:"120",sortable:"","sort-method":M},{default:(0,l.k6)((({row:e})=>[(0,l.eW)((0,u.v_)(e.quantity-e.usedQuantity)+"/"+(0,u.v_)(e.quantity),1)])),_:1}),(0,l.bF)(i,{label:"总金额",width:"120",sortable:"","sort-method":V},{default:(0,l.k6)((({row:e})=>[(0,l.eW)((0,u.v_)(R(e.price)),1)])),_:1}),(0,l.bF)(i,{prop:"phone",label:"买家",width:"120"}),(0,l.bF)(i,{label:"订单时间",width:"180",sortable:"","sort-method":W},{default:(0,l.k6)((({row:e})=>[(0,l.eW)((0,u.v_)((0,r.R1)(c())(e.date).format("YYYY-MM-DD HH:mm:ss")),1)])),_:1}),(0,l.bF)(i,{label:"过期时间",width:"180",sortable:"","sort-method":q},{default:(0,l.k6)((({row:e})=>[(0,l.eW)((0,u.v_)((0,r.R1)(c())(e.expiry).format("YYYY-MM-DD HH:mm:ss")),1)])),_:1}),(0,l.bF)(i,{label:"操作",width:"150",fixed:"right"},{default:(0,l.k6)((e=>[(0,l.bF)(g,{size:"small",type:"danger",onClick:t=>S(e.row)},{default:(0,l.k6)((()=>a[4]||(a[4]=[(0,l.eW)("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[F,t.value]])])),_:1})])}}}),y=a(9996);const I=(0,y.A)(w,[["__scopeId","data-v-0d2d4a10"]]);var F=I},5312:function(e,t,a){a.d(t,{WF:function(){return r},o8:function(){return u}});a(7335),a(7898);var l=a(9077);const r=async()=>{const e=await(0,l.A)({url:"/queryAllMPOrders",method:"get"});return{...e,data:e.data.map((e=>({id:parseInt(e.id),productId:parseInt(e.productId),quantity:parseInt(e.quantity),usedQuantity:parseInt(e.usedQuantity),price:parseFloat(e.price),phone:e.phone,date:e.date,expiry:e.expiry,status:e.status})))}},u=e=>(0,l.A)({url:"/deleteMPOrder",method:"post",params:{mpOrderId:e}})},5663:function(e,t,a){a.d(t,{$u:function(){return s},AO:function(){return c},Fs:function(){return o},Hf:function(){return i},Q9:function(){return v},Z1:function(){return r},bW:function(){return u},d$:function(){return n},gg:function(){return d},vu:function(){return p}});var l=a(9077);const r=()=>(0,l.A)({url:"/queryServices",method:"get"}),u=()=>(0,l.A)({url:"/queryCategories",method:"get"}),o=()=>(0,l.A)({url:"/queryBrands",method:"get"}),n=()=>(0,l.A)({url:"/queryProducts",method:"get"}),d=()=>(0,l.A)({url:"/queryMPProducts",method:"get"}),s=e=>(0,l.A)({url:"/createMPProduct",method:"post",data:e}),i=e=>(0,l.A)({url:"/modifyMPProduct",method:"post",data:e}),c=e=>(0,l.A)({url:"/deleteMPProduct",method:"post",params:{mpProductId:e}}),p=(e,t)=>{const a=new FormData;return a.append("productId",e.toString()),a.append("image",t),(0,l.A)({url:"/setProductImage",method:"post",data:a,headers:{"Content-Type":"multipart/form-data"}})},v=()=>(0,l.A)({url:"/queryProductImages",method:"get"})}}]);
//# sourceMappingURL=28.df85b80c.js.map