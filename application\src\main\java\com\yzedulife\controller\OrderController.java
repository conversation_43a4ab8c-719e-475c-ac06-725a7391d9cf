package com.yzedulife.controller;

import com.wechat.pay.java.service.payments.model.Transaction;
import com.yzedulife.annotation.Token;
import com.yzedulife.annotation.MembershipToken;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.common.domain.CommonErrorCode;
import com.yzedulife.convert.MPOrderConvert;
import com.yzedulife.convert.MPOrderDetailConvert;
import com.yzedulife.response.Response;
import com.yzedulife.util.SecurityUtil;
import com.yzedulife.vo.MPOrderVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@RestController
@Tag(name = "订单 接口")
public class OrderController {
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderDetailService orderDetailService;
    @Autowired
    private MPOrderService mpOrderService;
    @Autowired
    private MPOrderDetailService mpOrderDetailService;
    @Autowired
    private WxPayService wxPayService;
    @Autowired
    private ProductService productService;
    @Autowired
    private ServiceService serviceService;
    @Autowired
    private PinCache pinCache;

    @MembershipToken
    @Operation(summary = "获取用户所有小程序订单")
    @GetMapping("/queryMPOrders")
    public Response queryMPOrders() {
        String phone = SecurityUtil.getPhone();
        List<MPOrderDTO> mpOrderDTOs = mpOrderService.queryBatch(Long.parseLong(phone));
        // 检查并更新订单状态
        mpOrderDTOs.forEach(dto -> {
            // 待支付订单过期
            if (dto.getStatus() != null && dto.getStatus().equals("PENDING")) { // 待支付
                Transaction transaction = wxPayService.queryOrderByOutTradeNo(dto.getId().toString());
                if (transaction.getTradeState().toString().equals("CLOSED")) { // 订单支付时间用尽, 订单状态参数：https://pay.weixin.qq.com/doc/v3/merchant/4012791859
                    dto.setStatus("EXPIRED");
                    mpOrderService.modify(dto);
                }
            }
            // 使用中订单过期
            if (dto.getStatus() != null && dto.getStatus().equals("ACTIVE")) { // 使用中
                if (dto.getExpiry().isBefore(LocalDateTime.now())) { // 订单过期
                    dto.setStatus("EXPIRED");
                    mpOrderService.modify(dto);
                    wxPayService.refund(dto);
                }
            }
        });
        Collections.reverse(mpOrderDTOs); // 顺序反转，按时间倒序
        return Response.success().data(MPOrderConvert.INSTANCE.dto2resBatch(mpOrderDTOs));
    }

    @MembershipToken
    @Operation(summary = "获取小程序订单使用明细")
    @GetMapping("/queryMPOrderDetails")
    public Response queryMPOrderDetails(@RequestParam Long mpOrderId) {
        List<MPOrderDetailDTO> mpOrderDetailDTOs = mpOrderDetailService.queryBatch(mpOrderId);
        return Response.success().data(MPOrderDetailConvert.INSTANCE.dto2resBatch(mpOrderDetailDTOs));
    }

    @Token
    @Operation(summary = "获取所有小程序订单")
    @GetMapping("/queryAllMPOrders")
    public Response queryAllMPOrders() {
        List<MPOrderDTO> mpOrderDTOs = mpOrderService.queryBatch();
        // 检查并更新订单状态
        mpOrderDTOs.forEach(dto -> {
            // 待支付订单过期
            if (dto.getStatus() != null && dto.getStatus().equals("PENDING")) { // 待支付
                Transaction transaction = wxPayService.queryOrderByOutTradeNo(dto.getId().toString());
                if (transaction.getTradeState().toString().equals("CLOSED")) { // 订单支付时间用尽, 订单状态参数：https://pay.weixin.qq.com/doc/v3/merchant/4012791859
                    dto.setStatus("EXPIRED");
                    mpOrderService.modify(dto);
                }
            }
            // 使用中订单过期
            if (dto.getStatus() != null && dto.getStatus().equals("ACTIVE")) { // 使用中
                if (dto.getExpiry().isBefore(LocalDateTime.now())) { // 订单过期
                    dto.setStatus("EXPIRED");
                    mpOrderService.modify(dto);
                    wxPayService.refund(dto);
                }
            }
        });
        Collections.reverse(mpOrderDTOs); // 顺序反转，按时间倒序
        return Response.success().data(MPOrderConvert.INSTANCE.dto2resBatch(mpOrderDTOs));
    }

    @Token
    @Operation(summary = "修改小程序订单")
    @PostMapping("/modifyMPOrder")
    public Response modifyMPOrder(@RequestBody MPOrderVO mpOrderVO) {
        mpOrderService.modify(mpOrderVO.toDTO());
        return Response.success().msg("修改成功");
    }

    @Token
    @Operation(summary = "删除小程序订单")
    @PostMapping("/deleteMPOrder")
    public Response deleteMPOrder(@RequestParam Long mpOrderId) {
        mpOrderService.delete(mpOrderId);
        return Response.success().msg("删除成功");
    }

    @Token
    @Operation(summary = "删除小程序订单")
    @PostMapping("/deleteMPOrders")
    public Response deleteMPOrders(@RequestParam List<Long> mpOrderIds) {
        mpOrderService.deleteBatch(mpOrderIds);
        return Response.success().msg("删除成功");
    }


    @MembershipToken
    @Operation(summary = "生成核销码")
    @PostMapping("/generatePin")
    public Response generatePin(@RequestParam Long mpOrderId) {
        String pin = pinCache.generatePin(mpOrderId);
        return Response.success().data(pin);
    }

    @Token
    @Operation(summary = "核销商品")
    @PostMapping("/verifyPin")
    public Response verifyPin(@RequestParam String plate,
                            @RequestParam String pin) {
        Long mpOrderId = pinCache.validatePin(pin);
        // 验证码不存在或已过期
        if (mpOrderId == null)
            throw new BusinessException(CommonErrorCode.E_400003);

        if (!mpOrderService.isExist(mpOrderId)) {
            return Response.error().msg("订单不存在");
        }

        MPOrderDTO mpOrderDTO = mpOrderService.query(mpOrderId);
        ProductDTO productDTO = productService.query(mpOrderDTO.getProductId());

        // 校验订单状态
        switch (mpOrderDTO.getStatus()) {
            case "PENDING":
                return Response.error().msg("订单未支付");
            case "USED":
                return Response.error().msg("此订单已用尽");
            case "EXPIRED":
                return Response.error().msg("订单已过期");
            case "REFUNDED":
                return Response.error().msg("订单已退款");
        }

        /***
         * 计算本笔订单金额
         * 除不尽的情况下，单独计算最后一笔订单
         */
        Double price = new BigDecimal(mpOrderDTO.getPrice() / (double)mpOrderDTO.getQuantity()).setScale(2, RoundingMode.HALF_UP).doubleValue();
        if (mpOrderDTO.getUsedQuantity() == mpOrderDTO.getQuantity() - 1) {
            price = productDTO.getPrice() - price * mpOrderDTO.getUsedQuantity();
        }

        /***
         * 老系统业务
         */
        String serviceName = serviceService.query(productDTO.getServiceId()).getName();
        // 减少仓库库存
        OrderDTO orderDTO = new OrderDTO(
                null,
                plate,
                LocalDate.now(),
                price,
                serviceName,
                "微信支付");
        orderDTO = orderService.create(orderDTO);
        // 添加订单记录
        OrderDetailDTO orderDetailDTO = new OrderDetailDTO(
                null,
                orderDTO.getId().toString(),
                productDTO.getId(),
                1L,
                price,
                serviceName,
                plate,
                productDTO.getName(),
                LocalDate.now());
        orderDetailService.create(orderDetailDTO);

        // 使用数+1
        mpOrderDTO.setUsedQuantity(mpOrderDTO.getUsedQuantity() + 1);
        mpOrderService.modify(mpOrderDTO);
        // 使用记录
        mpOrderDetailService.create(mpOrderId, LocalDateTime.now());

        // 检验是否用尽
        if (mpOrderDTO.getUsedQuantity().equals(mpOrderDTO.getQuantity())) {
            mpOrderDTO.setStatus("USED");
            mpOrderService.modify(mpOrderDTO);
        }

        return Response.success();
    }
}
