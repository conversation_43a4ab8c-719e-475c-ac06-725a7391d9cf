package com.yzedulife.convert;

import com.yzedulife.service.dto.BrandDTO;
import com.yzedulife.response.BrandResponse;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
@Mapper
public interface BrandConvert {
    BrandConvert INSTANCE = Mappers.getMapper(BrandConvert.class);

    BrandResponse dto2res(BrandDTO brandDTO);
    List<BrandResponse> dto2resBatch(List<BrandDTO> brandDTOs);
}
