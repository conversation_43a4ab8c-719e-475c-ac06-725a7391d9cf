package com.yzedulife.controller;

import com.yzedulife.annotation.Token;
import com.yzedulife.convert.MPProductConvert;
import com.yzedulife.response.Response;
import com.yzedulife.convert.BrandConvert;
import com.yzedulife.convert.CategoryConvert;
import com.yzedulife.convert.ProductConvert;
import com.yzedulife.convert.ServiceConvert;
import com.yzedulife.vo.MPProductVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Tag(name = "产品 接口")
public class ProductController {
    @Autowired
    private ProductService productService;
    @Autowired
    private ServiceService serviceService;
    @Autowired
    private CategoryService categoryService;
    @Autowired
    private BrandService brandService;
    @Autowired
    private MPProductService mpProductService;

    @Operation(summary = "获取服务列表")
    @GetMapping("/queryServices")
    public Response queryServices() {
        List<ServiceDTO> serviceDTOs = serviceService.queryBatch();
        return Response.success().data(ServiceConvert.INSTANCE.dto2resBatch(serviceDTOs));
    }

    @Operation(summary = "获取类型列表")
    @GetMapping("/queryCategories")
    public Response queryCategories() {
        List<CategoryDTO> categoryDTOs = categoryService.queryBatch();
        return Response.success().data(CategoryConvert.INSTANCE.dto2resBatch(categoryDTOs));
    }

    @Operation(summary = "获取品牌列表")
    @GetMapping("/queryBrands")
    public Response queryBrands() {
        List<BrandDTO> brandDTOs = brandService.queryBatch();
        return Response.success().data(BrandConvert.INSTANCE.dto2resBatch(brandDTOs));
    }

    @Operation(summary = "获取产品信息")
    @GetMapping("/queryProducts")
    public Response queryProducts() {
        List<ProductDTO> productDTOs = productService.queryBatch();
        return Response.success().data(ProductConvert.INSTANCE.dto2resBatch(productDTOs));
    }

    @Operation(summary = "获取小程序产品信息")
    @GetMapping("/queryMPProducts")
    public Response queryMPProducts() {
        List<MPProductDTO> mpProductDTOs = mpProductService.queryBatch();
        return Response.success().data(MPProductConvert.INSTANCE.dto2resBatch(mpProductDTOs));
    }

    @Token
    @Operation(summary = "创建新产品")
    @PostMapping("/createMPProduct")
    public Response createMPProduct(@RequestBody MPProductVO mpProductVO) {
        MPProductDTO mpProductDTO = mpProductService.create(mpProductVO.toDTO());
        return Response.success().data(MPProductConvert.INSTANCE.dto2res(mpProductDTO));
    }

    @Token
    @Operation(summary = "修改产品信息")
    @PostMapping("/modifyMPProduct")
    public Response modifyMPProduct(@RequestBody MPProductVO mpProductVO) {
        mpProductService.modify(mpProductVO.toDTO());
        return Response.success();
    }

    @Token
    @Operation(summary = "删除产品")
    @PostMapping("/deleteMPProduct")
    public Response deleteMPProduct(@RequestParam Long mpProductId) {
        mpProductService.delete(mpProductId);
        return Response.success();
    }

    @Token
    @Operation(summary = "删除产品")
    @PostMapping("/deleteMPProducts")
    public Response deleteMPProducts(@RequestParam List<Long> mpProductIds) {
        mpProductService.deleteBatch(mpProductIds);
        return Response.success();
    }

    @Token
    @Operation(summary = "设置产品图片")
    @PostMapping("/setProductImage")
    public Response setProductImage(@RequestParam Long productId, @RequestParam MultipartFile image) throws Exception {
        try {
            String uploadDir = Paths.get("covers").toAbsolutePath().toString();
            File directory = new File(uploadDir);
            if (!directory.exists()) directory.mkdir();

            String extension = image.getOriginalFilename().substring(image.getOriginalFilename().lastIndexOf("."));
            String fileName = productId + extension;
            String filePath = uploadDir + File.separator + fileName;

            File dest = new File(filePath);
            image.transferTo(dest);

            return Response.success().data(fileName);
        } catch (IOException e) {
            return Response.error().msg("图片上传失败");
        }
    }

    @Operation(summary = "获取所有产品图片")
    @GetMapping("/queryProductImages")
    public Response queryProductImages() {
        try {
            String uploadDir = "covers";
            File directory = new File(uploadDir);
            if (!directory.exists()) {
                return Response.success().data(new ArrayList<>());
            }

            File[] files = directory.listFiles();
            List<Map<String, Object>> imageList = new ArrayList<>();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        String fileName = file.getName();
                        String productId = fileName.substring(0, fileName.lastIndexOf("."));
                        
                        Map<String, Object> imageInfo = new HashMap<>();
                        imageInfo.put("productId", productId);
                        imageInfo.put("fileName", fileName);
                        imageList.add(imageInfo);
                    }
                }
            }
            
            return Response.success().data(imageList);
        } catch (Exception e) {
            return Response.error().msg("获取图片列表失败");
        }
    }

    @Operation(summary = "获取产品图片")
    @GetMapping("/productCover/{fileName}")
    public ResponseEntity<Resource> getProductImage(@PathVariable String fileName) {
        try {
            String uploadDir = "covers";
            File file = new File(uploadDir, fileName);
            if (!file.exists()) {
                return ResponseEntity.notFound().build();
            }

            Path path = file.toPath();
            Resource resource = new FileSystemResource(path);
            String contentType = Files.probeContentType(path);

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .body(resource);
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

}
