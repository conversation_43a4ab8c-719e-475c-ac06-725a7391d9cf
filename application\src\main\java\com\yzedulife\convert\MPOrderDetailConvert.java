package com.yzedulife.convert;

import com.yzedulife.response.MPOrderDetailResponse;
import com.yzedulife.service.dto.MPOrderDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MPOrderDetailConvert {
    MPOrderDetailConvert INSTANCE = Mappers.getMapper(MPOrderDetailConvert.class);

    MPOrderDetailResponse dto2res(MPOrderDetailDTO mpOrderDetailDTO);

    List<MPOrderDetailResponse> dto2resBatch(List<MPOrderDetailDTO> mpOrderDetailDTOs);
}
