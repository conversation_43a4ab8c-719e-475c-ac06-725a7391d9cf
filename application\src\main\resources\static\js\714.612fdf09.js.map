{"version": 3, "file": "js/714.612fdf09.js", "mappings": "4NAEO,MAAMA,EAAcC,IAChBC,EAAAA,EAAAA,GAAQ,CACXC,IAAK,cACLC,OAAQ,OACRC,OAAQ,CAAEJ,cCHZK,EAAa,CAAEC,MAAO,mBACtBC,EAAa,CAAED,MAAO,iBACtBE,EAAa,CAAEF,MAAO,cACtBG,EAAa,CAAC,OAQpB,OAA4BC,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,YACRC,KAAAA,CAAMC,GCMR,MAAMC,GAASC,EAAAA,EAAAA,MACTf,GAAWgB,EAAAA,EAAAA,IAAI,IACrB,IAAIC,EAA+B,KAGnC,MAAMC,EAAYC,UAChB,IAEE,MAAMC,EAAM,CAAEC,KAAM,CAAErB,SAAU,UAAYsB,KAAKC,QACjDvB,EAASwB,MAAQJ,EAAIC,KAAKrB,SAG1ByB,GACF,CAAE,MAAOC,GACPC,EAAAA,GAAUD,MAAM,mBAChBE,QAAQF,MAAM,WAAYA,EAC5B,GAIID,EAA2BA,KAE3BR,GACFY,cAAcZ,GAIhBa,YAAW,KACLb,IACFY,cAAcZ,GACdA,EAAgB,KAEhBC,IACF,GACC,MAGHD,EAAgBc,OAAOC,aAAYb,UACjC,IACE,MAAMC,QAAYrB,EAAWC,EAASwB,OACtC,IAAiB,IAAbJ,EAAIC,KAEN,OAIED,EAAIC,OACgB,OAAlBJ,IACFY,cAAcZ,GACdA,EAAgB,MAIlBgB,aAAaC,QAAQ,QAASd,EAAIC,MAGlCM,EAAAA,GAAUQ,QAAQ,QAClBrB,EAAOsB,KAAK,cAEhB,CAAE,MAAOV,GACPE,QAAQF,MAAM,YAAaA,EAC7B,IACC,IAAK,EDYV,OCRAW,EAAAA,EAAAA,KAAU,KACRnB,GAAU,KAIZoB,EAAAA,EAAAA,KAAY,KACNrB,IACFY,cAAcZ,GACdA,EAAgB,KAClB,IDDK,CAACsB,EAAUC,MACRC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOrC,EAAY,EAC3DsC,EAAAA,EAAAA,IAAoB,MAAOpC,EAAY,EACrCoC,EAAAA,EAAAA,IAAoB,MAAOnC,EAAY,CACpCR,EAASwB,QACLiB,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO,CACxCE,IAAK,EACLC,IAAK,oDAAoDC,mBAAmB9C,EAASwB,gCACrFuB,IAAK,QACLzC,MAAO,cACN,KAAM,EAAGG,KACZuC,EAAAA,EAAAA,IAAoB,IAAI,KAE9BR,EAAO,KAAOA,EAAO,IAAKG,EAAAA,EAAAA,IAAoB,IAAK,CAAErC,MAAO,YAAc,iBAAkB,QAIlG,I,UE3GA,MAAM2C,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://admin-web/./src/api/user.ts", "webpack://admin-web/./src/views/login/LoginView.vue?969a", "webpack://admin-web/./src/views/login/LoginView.vue", "webpack://admin-web/./src/views/login/LoginView.vue?6c8d"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\nexport const checkLogin = (deviceId: string) => {\r\n    return request({\r\n        url: '/checkLogin',\r\n        method: 'post',\r\n        params: { deviceId }\r\n    })\r\n}", "import { defineComponent as _defineComponent } from 'vue'\nimport { openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"login-container\" }\nconst _hoisted_2 = { class: \"login-content\" }\nconst _hoisted_3 = { class: \"qrcode-box\" }\nconst _hoisted_4 = [\"src\"]\n\nimport { ref, onMounted, onUnmounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage } from 'element-plus'\nimport { checkLogin } from '@/api/user'\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'LoginView',\n  setup(__props) {\n\nconst router = useRouter()\nconst deviceId = ref('')\nlet checkInterval: number | null = null\n\n// 获取登录二维码\nconst getQRCode = async () => {\n  try {\n    // 临时模拟一个结果，请替换为实际的API调用\n    const res = { data: { deviceId: 'device_' + Date.now() } }\n    deviceId.value = res.data.deviceId\n    \n    // 开始轮询检查登录状态\n    startCheckingLoginStatus()\n  } catch (error) {\n    ElMessage.error('获取二维码失败，请刷新页面重试')\n    console.error('获取二维码失败:', error)\n  }\n}\n\n// 开始轮询检查登录状态\nconst startCheckingLoginStatus = () => {\n  // 清除可能存在的旧定时器\n  if (checkInterval) {\n    clearInterval(checkInterval)\n  }\n  \n  // 设置二维码过期时间为2分钟\n  setTimeout(() => {\n    if (checkInterval) {\n      clearInterval(checkInterval)\n      checkInterval = null\n      // 二维码过期后，重新获取\n      getQRCode()\n    }\n  }, 2 * 60 * 1000)\n  \n  // 每秒检查一次登录状态\n  checkInterval = window.setInterval(async () => {\n    try {\n      const res = await checkLogin(deviceId.value)\n      if (res.data === false) {\n        // 未登录，继续等待\n        return\n      }\n      \n      // 登录成功，保存 token 并跳转\n      if (res.data) {\n        if (checkInterval !== null) {\n          clearInterval(checkInterval)\n          checkInterval = null\n        }\n        \n        // 保存 token\n        localStorage.setItem('token', res.data)\n        \n        // 跳转到仪表盘\n        ElMessage.success('登录成功')\n        router.push('/dashboard')\n      }\n    } catch (error) {\n      console.error('检查登录状态失败:', error)\n    }\n  }, 1000)\n}\n\n// 组件挂载时获取二维码\nonMounted(() => {\n  getQRCode()\n})\n\n// 组件卸载时清除定时器\nonUnmounted(() => {\n  if (checkInterval) {\n    clearInterval(checkInterval)\n    checkInterval = null\n  }\n})\n\nreturn (_ctx: any,_cache: any) => {\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createElementVNode(\"div\", _hoisted_3, [\n        (deviceId.value)\n          ? (_openBlock(), _createElementBlock(\"img\", {\n              key: 0,\n              src: `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(deviceId.value)}&size=200x200&margin=10`,\n              alt: \"登录二维码\",\n              class: \"qrcode-img\"\n            }, null, 8, _hoisted_4))\n          : _createCommentVNode(\"\", true)\n      ]),\n      _cache[0] || (_cache[0] = _createElementVNode(\"p\", { class: \"scan-tip\" }, \"请使用微信小程序扫一扫登录\", -1))\n    ])\n  ]))\n}\n}\n\n})", "<template>\n  <div class=\"login-container\">\n    <div class=\"login-content\">\n      <div class=\"qrcode-box\">\n        <img \n          v-if=\"deviceId\" \n          :src=\"`https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(deviceId)}&size=200x200&margin=10`\"\n          alt=\"登录二维码\"\n          class=\"qrcode-img\"\n        />\n      </div>\n      <p class=\"scan-tip\">请使用微信小程序扫一扫登录</p>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted, onUnmounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage } from 'element-plus'\nimport { checkLogin } from '@/api/user'\n\nconst router = useRouter()\nconst deviceId = ref('')\nlet checkInterval: number | null = null\n\n// 获取登录二维码\nconst getQRCode = async () => {\n  try {\n    // 临时模拟一个结果，请替换为实际的API调用\n    const res = { data: { deviceId: 'device_' + Date.now() } }\n    deviceId.value = res.data.deviceId\n    \n    // 开始轮询检查登录状态\n    startCheckingLoginStatus()\n  } catch (error) {\n    ElMessage.error('获取二维码失败，请刷新页面重试')\n    console.error('获取二维码失败:', error)\n  }\n}\n\n// 开始轮询检查登录状态\nconst startCheckingLoginStatus = () => {\n  // 清除可能存在的旧定时器\n  if (checkInterval) {\n    clearInterval(checkInterval)\n  }\n  \n  // 设置二维码过期时间为2分钟\n  setTimeout(() => {\n    if (checkInterval) {\n      clearInterval(checkInterval)\n      checkInterval = null\n      // 二维码过期后，重新获取\n      getQRCode()\n    }\n  }, 2 * 60 * 1000)\n  \n  // 每秒检查一次登录状态\n  checkInterval = window.setInterval(async () => {\n    try {\n      const res = await checkLogin(deviceId.value)\n      if (res.data === false) {\n        // 未登录，继续等待\n        return\n      }\n      \n      // 登录成功，保存 token 并跳转\n      if (res.data) {\n        if (checkInterval !== null) {\n          clearInterval(checkInterval)\n          checkInterval = null\n        }\n        \n        // 保存 token\n        localStorage.setItem('token', res.data)\n        \n        // 跳转到仪表盘\n        ElMessage.success('登录成功')\n        router.push('/dashboard')\n      }\n    } catch (error) {\n      console.error('检查登录状态失败:', error)\n    }\n  }, 1000)\n}\n\n// 组件挂载时获取二维码\nonMounted(() => {\n  getQRCode()\n})\n\n// 组件卸载时清除定时器\nonUnmounted(() => {\n  if (checkInterval) {\n    clearInterval(checkInterval)\n    checkInterval = null\n  }\n})\n</script>\n\n<style scoped>\n.login-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background-color: #f0f2f5;\n}\n\n.login-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background-color: #fff;\n  padding: 30px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.qrcode-box {\n  width: 200px;\n  height: 200px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.qrcode-img {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n}\n\n.scan-tip {\n  text-align: center;\n  font-size: 16px;\n  color: #606266;\n  margin: 0;\n}\n</style> ", "import script from \"./LoginView.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./LoginView.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./LoginView.vue?vue&type=style&index=0&id=3485a2cd&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/.pnpm/vue-loader@17.4.2_@vue+compiler-sfc@3.5.13_vue@3.5.13_typescript@5.8.3__webpack@5.98.0/node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-3485a2cd\"]])\n\nexport default __exports__"], "names": ["checkLogin", "deviceId", "request", "url", "method", "params", "_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_defineComponent", "__name", "setup", "__props", "router", "useRouter", "ref", "checkInterval", "getQRCode", "async", "res", "data", "Date", "now", "value", "startCheckingLoginStatus", "error", "ElMessage", "console", "clearInterval", "setTimeout", "window", "setInterval", "localStorage", "setItem", "success", "push", "onMounted", "onUnmounted", "_ctx", "_cache", "_openBlock", "_createElementBlock", "_createElementVNode", "key", "src", "encodeURIComponent", "alt", "_createCommentVNode", "__exports__"], "sourceRoot": ""}