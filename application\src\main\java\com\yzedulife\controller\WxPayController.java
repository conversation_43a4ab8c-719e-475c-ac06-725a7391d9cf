package com.yzedulife.controller;

import com.alibaba.fastjson.JSONObject;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayWithRequestPaymentResponse;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.yzedulife.annotation.MembershipToken;
import com.yzedulife.response.Response;
import com.yzedulife.service.dto.MPOrderDTO;
import com.yzedulife.service.dto.MPProductDTO;
import com.yzedulife.service.service.MPOrderService;
import com.yzedulife.service.service.MPProductService;
import com.yzedulife.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@Tag(name = "微信支付 接口")
public class WxPayController {
    @Autowired
    private MPProductService mpProductService;
    @Autowired
    private MPOrderService mpOrderService;
    @Autowired
    private WxPayService wxPayService;
    @Autowired
    private WechatPayCache wechatPayCache;

    @MembershipToken
    @Operation(summary = "下单并返回微信支付参数")
    @PostMapping("/prepayNew")
    public Response prepayNew(@RequestParam Long mpProductId) {
        String openid = SecurityUtil.getOpenid();
        String phone = SecurityUtil.getPhone();
        MPProductDTO mpProductDTO = mpProductService.query(mpProductId);
        MPOrderDTO mpOrderDTO = new MPOrderDTO(mpProductDTO, Long.parseLong(phone));
        mpOrderDTO = mpOrderService.create(mpOrderDTO);
        PrepayWithRequestPaymentResponse prepayResponse = wxPayService.prepay(mpProductDTO, openid, mpOrderDTO.getId().toString());
        wechatPayCache.cachePaymentParams(mpOrderDTO.getId(), prepayResponse);
        return Response.success().data(prepayResponse);
    }

    @MembershipToken
    @Operation(summary = "获取缓存微信支付参数")
    @PostMapping("/prepay")
    public Response prepay(@RequestParam Long mpOrderId) {
        String phone = SecurityUtil.getPhone();
        if (!mpOrderService.isExist(mpOrderId)) {
            return Response.error().msg("订单不存在");
        } else if (!mpOrderService.query(mpOrderId).getPhone().toString().equals(phone)) {
            return Response.error().msg("订单不属于当前用户");
        }

        PrepayWithRequestPaymentResponse prepayResponse = wechatPayCache.getPaymentParams(mpOrderId);
        return Response.success().data(prepayResponse);
    }

    @MembershipToken
    @Operation(summary = "查询订单")
    @GetMapping("/getOrderState")
    public Response getOrderState(@RequestParam Long mpOrderId) {
        String phone = SecurityUtil.getPhone();
        if (!mpOrderService.isExist(mpOrderId)) {
            return Response.error().msg("订单不存在");
        } else if (!mpOrderService.query(mpOrderId).getPhone().toString().equals(phone)) {
            return Response.error().msg("订单不属于当前用户");
        }

        Transaction transaction = wxPayService.queryOrderByOutTradeNo(mpOrderId.toString());
        return Response.success().data(transaction.getTradeStateDesc());
    }

    @MembershipToken
    @Operation(summary = "关闭订单")
    @PostMapping("/closeOrder")
    public Response closeOrder(@RequestParam Long mpOrderId) {
        String phone = SecurityUtil.getPhone();
        if (!mpOrderService.isExist(mpOrderId)) {
            return Response.error().msg("订单不存在");
        } else if (!mpOrderService.query(mpOrderId).getPhone().toString().equals(phone)) {
            return Response.error().msg("订单不属于当前用户");
        }

        wxPayService.closeOrder(mpOrderId.toString());
        return Response.success();
    }

    @MembershipToken
    @Operation(summary = "退款")
    @PostMapping("/refund")
    public Response refund(@RequestParam Long mpOrderId) {
        MPOrderDTO mpOrderDTO = mpOrderService.query(mpOrderId);
        String phone = SecurityUtil.getPhone();
        if (!mpOrderService.isExist(mpOrderId)) {
            return Response.error().msg("订单不存在");
        } else if (!mpOrderDTO.getPhone().toString().equals(phone)) {
            return Response.error().msg("订单不属于当前用户");
        }

        // 生成随机退款单号
        Long refundId = (long) (Math.random() * 900000000L + 100000000L);
        mpOrderDTO.setRefundId(refundId);
        mpOrderService.modify(mpOrderDTO);
//        wxPayService.refund(mpOrderDTO);
        wxPayService.refundAll(mpOrderDTO); // 全额退款(调试用)
        return Response.success();
    }

    @Operation(summary = "微信支付成功回调通知")
    @PostMapping("/paySuccessNotify")
    public Response paySuccessNotify(@RequestBody JSONObject jsonObject) {
        String mpOrderId = wxPayService.decrypt(jsonObject);
        MPOrderDTO mpOrderDTO = mpOrderService.query(Long.parseLong(mpOrderId));
        mpOrderDTO.setStatus("ACTIVE");
        mpOrderService.modify(mpOrderDTO);

        return Response.success();
    }

    @Operation(summary = "微信退款成功回调通知")
    @PostMapping("/refundSuccessNotify")
    public Response refundSuccessNotify(@RequestBody JSONObject jsonObject) {
        String mpOrderId = wxPayService.decrypt(jsonObject);
        MPOrderDTO mpOrderDTO = mpOrderService.query(Long.parseLong(mpOrderId));
        if (mpOrderDTO.getStatus().equalsIgnoreCase("ACTIVE")) { // 是否为主动退款
            mpOrderDTO.setStatus("REFUNDED");
            mpOrderService.modify(mpOrderDTO);
        }
        return Response.success();
    }
}
