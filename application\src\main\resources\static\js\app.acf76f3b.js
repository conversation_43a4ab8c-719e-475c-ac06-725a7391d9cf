(function(){"use strict";var e={8636:function(e,t,n){var o=n(1417),r=n(4922),a=n(5201),l=n(1402),u="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADcAAAAlCAMAAADRG4HkAAAArlBMVEVHcEwqLYcUFkQqKnwtMI4dH2AjJXAMDCccGk4EBAwsL44pK4IpK4ItMI8bHVkkJW4pK4MrLosoK4InKn0qLYknKXwpLIUkJnQsL4wgI2oqLYgqLIYnKn4rLookJ3YhJG8iJXAsL4wgI2ohGk4jJnInKn4jJXAlKHi+JiwnKX2LGx6aHiMnKn+5JSuYHiKwIyiFHCSgHyN9GB2mISa1JCq0JClyFBEuMZItMJAuMZN/4A8NAAAAN3RSTlMAzgti+CE7BhID87Sq/BtHr+Kfd9NmvVPuMLbDjtxsNEHoKRZPf0ta+5c0Q4fxUrg6XiCO380oMeJGAgAAArFJREFUSMedlWl3ojAUhkkgEJGEfZN9E2m1eyf4///YBLBVZ8B6+p7DB0ie3Ju7IQh3CCJZjDeNItwtSDPf7rwkkBjb3MUpVViPBGEn/cQpuNTTeOMUUs8utcytMMrdvQEm4vhNSJFhxY2/mqfkvRaZ6rWNSWY4IH/e5jnr0sQ1h4b1x6dFjjFyg3ufdzROPMtupEXu5bma5SiF/JLqIvfw8bCchFvc4fWX3PsvuWflBnfKRU+koEjAmHERjnE5fOJFLksSZ9ybtrpcogqeTTwePt5utAzFcD5Pj7vDYkCVUvd9v663260+alv7rdjmlK897Q6PS7fTCokQ6UqE9ER19kh43e0WON9kx9kS5V9Bxrn5Cs3MIwvAZv2vNp7Dqw/oz/McNJjayVj5X5DWhsS0l88rDrm2bbtUEEmy1Jr8ULcg7YPY7Bs7LacQGkOr9kYGzPzW+KgLLwPjbbUxpys7UNVAJUXgf82wMPfFNLXt1G1ruaxOqW8Dk6jD3v3klRLKeRr1vT1CesxnmEr6SURSC2DY8oCuGiYZaS6H8MuF0iqYZI2vle+6rngp/u7WY8dijfRmjM6eJ6ywavjzTMW+FTAgfxWJyYzszvm9yiPmTPGEG2ZhAd+HYYGumTUGqiZRpTTNKZcLPKWTjS4VkKOOnnaqLuSSdVp3yzksTKfzYFSEQkv2w10jAwpiX9i1v5WpUHYzyfc7JFA5l+VGlXROeNxRCtpQzoIjI7yBnK0ge2kFIcQYQ16ZK/5ge10KbaLynmLHBNXIBtw61VC8wWIEBjk8ibXpeFEEHCfaTAIgEyrDGTd4PgKpvMZDtaCExRVGqOI2MC+hvDMMQzsrDod/G1dF+TaNRXSrTHWnBp5lrM+VwP270HffxFbXeUWRnCJQjnOEDxR6O3lVxhVyDbH9C/YBdDWxpl87AAAAAElFTkSuQmCC",i=n(3013);const c={class:"logo-container"},s={key:0,class:"logo-text"},d={class:"header-left"},f={class:"header-right"},p={class:"el-dropdown-link"},m={class:"content"};var b=(0,r.pM)({__name:"MainLayout",setup(e){const t=(0,a.lq)(),n=(0,l.KR)(!1),o=()=>{n.value=!n.value},b=()=>{localStorage.removeItem("token"),window.location.href="/login"};return(e,a)=>{const h=(0,r.g2)("el-icon"),v=(0,r.g2)("el-menu-item"),g=(0,r.g2)("el-menu"),k=(0,r.g2)("el-aside"),A=(0,r.g2)("el-button"),y=(0,r.g2)("el-dropdown-item"),w=(0,r.g2)("el-dropdown-menu"),F=(0,r.g2)("el-dropdown"),E=(0,r.g2)("el-header"),C=(0,r.g2)("el-container");return(0,r.uX)(),(0,r.Wv)(C,{class:"layout-container"},{default:(0,r.k6)((()=>[(0,r.bF)(k,{width:n.value?"64px":"200px"},{default:(0,r.k6)((()=>[(0,r.Lk)("div",c,[a[0]||(a[0]=(0,r.Lk)("img",{src:u,alt:"Logo",class:"logo-img"},null,-1)),n.value?(0,r.Q3)("",!0):((0,r.uX)(),(0,r.CE)("span",s,"博华小程序后台"))]),(0,r.bF)(g,{"default-active":(0,l.R1)(t).path,class:"el-menu-vertical",collapse:n.value,style:{height:"calc(100% - 60px)"},router:!0,"background-color":"#304156","text-color":"#bfcbd9","active-text-color":"#409EFF"},{default:(0,r.k6)((()=>[(0,r.bF)(v,{index:"/dashboard"},{title:(0,r.k6)((()=>a[1]||(a[1]=[(0,r.eW)("仪表盘")]))),default:(0,r.k6)((()=>[(0,r.bF)(h,null,{default:(0,r.k6)((()=>[(0,r.bF)((0,l.R1)(i.Odometer))])),_:1})])),_:1}),(0,r.bF)(v,{index:"/product"},{title:(0,r.k6)((()=>a[2]||(a[2]=[(0,r.eW)("商品管理")]))),default:(0,r.k6)((()=>[(0,r.bF)(h,null,{default:(0,r.k6)((()=>[(0,r.bF)((0,l.R1)(i.Goods))])),_:1})])),_:1}),(0,r.bF)(v,{index:"/order"},{title:(0,r.k6)((()=>a[3]||(a[3]=[(0,r.eW)("订单管理")]))),default:(0,r.k6)((()=>[(0,r.bF)(h,null,{default:(0,r.k6)((()=>[(0,r.bF)((0,l.R1)(i.List))])),_:1})])),_:1}),(0,r.bF)(v,{index:"/settings"},{title:(0,r.k6)((()=>a[4]||(a[4]=[(0,r.eW)("配置管理")]))),default:(0,r.k6)((()=>[(0,r.bF)(h,null,{default:(0,r.k6)((()=>[(0,r.bF)((0,l.R1)(i.Setting))])),_:1})])),_:1})])),_:1},8,["default-active","collapse"])])),_:1},8,["width"]),(0,r.bF)(C,null,{default:(0,r.k6)((()=>[(0,r.bF)(E,null,{default:(0,r.k6)((()=>[(0,r.Lk)("div",d,[(0,r.bF)(A,{link:"",onClick:o},{default:(0,r.k6)((()=>[(0,r.bF)(h,null,{default:(0,r.k6)((()=>[n.value?((0,r.uX)(),(0,r.Wv)((0,l.R1)(i.Expand),{key:1})):((0,r.uX)(),(0,r.Wv)((0,l.R1)(i.Fold),{key:0}))])),_:1})])),_:1})]),(0,r.Lk)("div",f,[(0,r.bF)(F,null,{dropdown:(0,r.k6)((()=>[(0,r.bF)(w,null,{default:(0,r.k6)((()=>[(0,r.bF)(y,{onClick:b},{default:(0,r.k6)((()=>a[6]||(a[6]=[(0,r.eW)("退出登录")]))),_:1})])),_:1})])),default:(0,r.k6)((()=>[(0,r.Lk)("span",p,[a[5]||(a[5]=(0,r.eW)(" 管理员 ")),(0,r.bF)(h,null,{default:(0,r.k6)((()=>[(0,r.bF)((0,l.R1)(i.ArrowDown))])),_:1})])])),_:1})])])),_:1}),(0,r.Lk)("main",m,[(0,r.RG)(e.$slots,"default")])])),_:3})])),_:3})}}}),h=n(9996);const v=(0,h.A)(b,[["__scopeId","data-v-0f97cae6"]]);var g=v;const k={class:"blank-layout"};function A(e,t){return(0,r.uX)(),(0,r.CE)("div",k,[(0,r.RG)(e.$slots,"default",{},void 0,!0)])}const y={},w=(0,h.A)(y,[["render",A],["__scopeId","data-v-75ebc252"]]);var F=w,E=(0,r.pM)({__name:"App",setup(e){const t=(0,a.lq)(),n=(0,r.EW)((()=>{const e=t.meta.layout||"main";return"blank"===e?F:g}));return(e,t)=>{const o=(0,r.g2)("router-view");return(0,r.uX)(),(0,r.Wv)((0,r.$y)(n.value),null,{default:(0,r.k6)((()=>[(0,r.bF)(o)])),_:1})}}});const C=E;var L=C;n(7335),n(499);const j=(0,a.aE)({history:(0,a.Bt)(""),routes:[{path:"/",redirect:"/dashboard"},{path:"/dashboard",name:"dashboard",component:()=>Promise.all([n.e(77),n.e(557)]).then(n.bind(n,9557)),meta:{requiresAuth:!0,layout:"main"}},{path:"/product",name:"product",component:()=>Promise.all([n.e(77),n.e(427)]).then(n.bind(n,3427)),meta:{requiresAuth:!0,layout:"main"}},{path:"/order",name:"order",component:()=>Promise.all([n.e(77),n.e(28)]).then(n.bind(n,3028)),meta:{requiresAuth:!0,layout:"main"}},{path:"/settings",name:"settings",component:()=>Promise.all([n.e(77),n.e(997)]).then(n.bind(n,997)),meta:{requiresAuth:!0,layout:"main"}},{path:"/login",name:"Login",component:()=>Promise.all([n.e(77),n.e(714)]).then(n.bind(n,1714)),meta:{layout:"blank",requiresAuth:!1}},{path:"/:pathMatch(.*)*",redirect:"/login"}]});j.beforeEach(((e,t,n)=>{const o=e.matched.some((e=>e.meta.requiresAuth)),r=!!localStorage.getItem("token");console.log(localStorage),console.log("路由:",e.path,"需要认证:",o,"已认证:",r),o&&!r?(console.log("未认证，重定向到登录页"),n("/login")):"/login"===e.path&&r?(console.log("已认证，重定向到仪表盘"),n("/dashboard")):n()}));var S=j,M=n(6495),_=(0,M.y$)({state:{},getters:{},mutations:{},actions:{},modules:{}}),x=n(4268),I=n(5261);n(9205);const R=(0,o.Ef)(L);for(const[O,K]of Object.entries(i))R.component(O,K);R.use(_),R.use(S),R.use(x.A,{locale:I.A}),R.mount("#app")}},t={};function n(o){var r=t[o];if(void 0!==r)return r.exports;var a=t[o]={exports:{}};return e[o].call(a.exports,a,a.exports,n),a.exports}n.m=e,function(){var e=[];n.O=function(t,o,r,a){if(!o){var l=1/0;for(s=0;s<e.length;s++){o=e[s][0],r=e[s][1],a=e[s][2];for(var u=!0,i=0;i<o.length;i++)(!1&a||l>=a)&&Object.keys(n.O).every((function(e){return n.O[e](o[i])}))?o.splice(i--,1):(u=!1,a<l&&(l=a));if(u){e.splice(s--,1);var c=r();void 0!==c&&(t=c)}}return t}a=a||0;for(var s=e.length;s>0&&e[s-1][2]>a;s--)e[s]=e[s-1];e[s]=[o,r,a]}}(),function(){n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,{a:t}),t}}(),function(){n.d=function(e,t){for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}}(),function(){n.f={},n.e=function(e){return Promise.all(Object.keys(n.f).reduce((function(t,o){return n.f[o](e,t),t}),[]))}}(),function(){n.u=function(e){return"js/"+e+"."+{28:"df85b80c",77:"d34b9415",427:"207beb5c",557:"756f67a5",714:"612fdf09",997:"e8fd0335"}[e]+".js"}}(),function(){n.miniCssF=function(e){return"css/"+e+"."+{28:"71eb5009",427:"54477b40",557:"ca200746",714:"f905cc74",997:"b71c4f05"}[e]+".css"}}(),function(){n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){var e={},t="admin-web:";n.l=function(o,r,a,l){if(e[o])e[o].push(r);else{var u,i;if(void 0!==a)for(var c=document.getElementsByTagName("script"),s=0;s<c.length;s++){var d=c[s];if(d.getAttribute("src")==o||d.getAttribute("data-webpack")==t+a){u=d;break}}u||(i=!0,u=document.createElement("script"),u.charset="utf-8",u.timeout=120,n.nc&&u.setAttribute("nonce",n.nc),u.setAttribute("data-webpack",t+a),u.src=o),e[o]=[r];var f=function(t,n){u.onerror=u.onload=null,clearTimeout(p);var r=e[o];if(delete e[o],u.parentNode&&u.parentNode.removeChild(u),r&&r.forEach((function(e){return e(n)})),t)return t(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:u}),12e4);u.onerror=f.bind(null,u.onerror),u.onload=f.bind(null,u.onload),i&&document.head.appendChild(u)}}}(),function(){n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){n.p=""}(),function(){if("undefined"!==typeof document){var e=function(e,t,o,r,a){var l=document.createElement("link");l.rel="stylesheet",l.type="text/css",n.nc&&(l.nonce=n.nc);var u=function(n){if(l.onerror=l.onload=null,"load"===n.type)r();else{var o=n&&n.type,u=n&&n.target&&n.target.href||t,i=new Error("Loading CSS chunk "+e+" failed.\n("+o+": "+u+")");i.name="ChunkLoadError",i.code="CSS_CHUNK_LOAD_FAILED",i.type=o,i.request=u,l.parentNode&&l.parentNode.removeChild(l),a(i)}};return l.onerror=l.onload=u,l.href=t,o?o.parentNode.insertBefore(l,o.nextSibling):document.head.appendChild(l),l},t=function(e,t){for(var n=document.getElementsByTagName("link"),o=0;o<n.length;o++){var r=n[o],a=r.getAttribute("data-href")||r.getAttribute("href");if("stylesheet"===r.rel&&(a===e||a===t))return r}var l=document.getElementsByTagName("style");for(o=0;o<l.length;o++){r=l[o],a=r.getAttribute("data-href");if(a===e||a===t)return r}},o=function(o){return new Promise((function(r,a){var l=n.miniCssF(o),u=n.p+l;if(t(l,u))return r();e(o,u,null,r,a)}))},r={524:0};n.f.miniCss=function(e,t){var n={28:1,427:1,557:1,714:1,997:1};r[e]?t.push(r[e]):0!==r[e]&&n[e]&&t.push(r[e]=o(e).then((function(){r[e]=0}),(function(t){throw delete r[e],t})))}}}(),function(){n.b=document.baseURI||self.location.href;var e={524:0};n.f.j=function(t,o){var r=n.o(e,t)?e[t]:void 0;if(0!==r)if(r)o.push(r[2]);else{var a=new Promise((function(n,o){r=e[t]=[n,o]}));o.push(r[2]=a);var l=n.p+n.u(t),u=new Error,i=function(o){if(n.o(e,t)&&(r=e[t],0!==r&&(e[t]=void 0),r)){var a=o&&("load"===o.type?"missing":o.type),l=o&&o.target&&o.target.src;u.message="Loading chunk "+t+" failed.\n("+a+": "+l+")",u.name="ChunkLoadError",u.type=a,u.request=l,r[1](u)}};n.l(l,i,"chunk-"+t,t)}},n.O.j=function(t){return 0===e[t]};var t=function(t,o){var r,a,l=o[0],u=o[1],i=o[2],c=0;if(l.some((function(t){return 0!==e[t]}))){for(r in u)n.o(u,r)&&(n.m[r]=u[r]);if(i)var s=i(n)}for(t&&t(o);c<l.length;c++)a=l[c],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return n.O(s)},o=self["webpackChunkadmin_web"]=self["webpackChunkadmin_web"]||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))}();var o=n.O(void 0,[504],(function(){return n(8636)}));o=n.O(o)})();
//# sourceMappingURL=app.acf76f3b.js.map