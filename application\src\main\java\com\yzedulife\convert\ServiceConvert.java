package com.yzedulife.convert;

import com.yzedulife.service.dto.ServiceDTO;
import com.yzedulife.response.ServiceResponse;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
@Mapper
public interface ServiceConvert {
    ServiceConvert INSTANCE = Mappers.getMapper(ServiceConvert.class);

    ServiceResponse dto2res(ServiceDTO serviceDTO);
    List<ServiceResponse> dto2resBatch(List<ServiceDTO> serviceDTOs);
}
